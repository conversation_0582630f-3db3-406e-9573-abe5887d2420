import type {NextApiRequest, NextApiResponse} from "next";
import {StatisticsOfAPIController} from "@/controller/statisticsOfAPIController";
import {LatestApi} from "@/entities/consolidated_data/LatestApi";

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    if (req.method !== "GET") {
        return res.status(405).json({error: "Method not allowed"});
    }

    try {
        const {days, api} = req.query;
        const latest_api = (api as string) || LatestApi.eodhd;

        // Validate latest_api
        if (!Object.values(LatestApi).includes(latest_api as LatestApi)) {
            return res.status(400).json({error: "Invalid API type"});
        }

        const statisticsController = new StatisticsOfAPIController();

        // Get current day consumption
        const todayResult = await statisticsController.getStatisticsOfAPICurrentComsumption(latest_api as LatestApi);
        let todayApiCredits = (todayResult as any)?.todayApiCredits || 0;

        // If days parameter is provided, get historical data
        if (days) {
            const daysNum = parseInt(days as string);
            if (isNaN(daysNum) || daysNum <= 0) {
                return res.status(400).json({error: "Invalid days parameter"});
            }

            const historicalData = await statisticsController.getApiCreditsForPastDays(daysNum, latest_api as LatestApi);

            // If todayApiCredits is 0, try to get it from historical data
            if (todayApiCredits === 0 && historicalData && historicalData.length > 0) {
                const today = new Date();
                const todayDateString = today.toISOString().split("T")[0];
                const todayData = historicalData.find((item: any) => item.date === todayDateString);
                if (todayData) {
                    todayApiCredits = Number((todayData as any).usage) || 0;
                }
            }

            return res.status(200).json({
                todayApiCredits,
                historicalData,
                days: daysNum,
                api: latest_api,
            });
        }

        // Return only today's consumption if no days parameter
        return res.status(200).json({
            todayApiCredits,
            api: latest_api,
        });
    } catch (error: any) {
        console.error("Error fetching API statistics:", error);
        return res.status(500).json({error: "Failed to fetch API statistics"});
    }
}
