import {NextApiRequest, NextApiResponse} from "next";
import {ListOfTickers} from "@/entities/consolidated_data/ListOfTickers";
import {SplitController} from "@/controller/splitsController";
import {addLogJobExecution} from "@/lib/agenda";
import {LogLevel} from "@/utils/types/logs/log";

/**
 * Test endpoint for splits batch processing
 * GET /api/splits/test-batch?limit=10&tickers=AAPL.US,MSFT.US
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    // Only allow GET requests
    if (req.method !== "GET") {
        return res.status(405).json({error: "Method not allowed"});
    }

    // Handle GET request to test batch processing
    if (req.method === "GET") {
        try {
            const {limit = "10", tickers} = req.query;
            
            let testTickers: ListOfTickers[];
            
            if (tickers && typeof tickers === "string") {
                // Use specific tickers if provided
                const tickerCodes = tickers.split(",").map(t => t.trim());
                testTickers = await ListOfTickers.findAll({
                    where: {
                        primary_ticker_eodhd: tickerCodes,
                        is_enable: 1
                    },
                    limit: parseInt(limit as string)
                });
            } else {
                // Get a sample of enabled tickers for testing
                testTickers = await ListOfTickers.findAll({
                    where: {
                        is_enable: 1
                    },
                    limit: parseInt(limit as string),
                    order: [["id", "ASC"]]
                });
            }

            if (testTickers.length === 0) {
                return res.status(404).json({error: "No enabled tickers found for testing"});
            }

            addLogJobExecution(LogLevel.INFO, "test-splits-batch", "Starting splits batch test", {
                tickersCount: testTickers.length,
                tickers: testTickers.map(t => t.primary_ticker_eodhd)
            });

            const startTime = Date.now();
            
            // Process splits using batch processing
            const splitController = new SplitController();
            await splitController.fillSplits(testTickers);
            
            const endTime = Date.now();
            const duration = endTime - startTime;

            addLogJobExecution(LogLevel.INFO, "test-splits-batch", "Splits batch test completed", {
                tickersCount: testTickers.length,
                duration: duration,
                durationMinutes: Math.round(duration / 60000 * 100) / 100
            });

            return res.status(200).json({
                success: true,
                message: "Splits batch processing test completed",
                data: {
                    tickersProcessed: testTickers.length,
                    tickers: testTickers.map(t => ({
                        id: t.id,
                        primary_ticker_eodhd: t.primary_ticker_eodhd,
                        name: t.name
                    })),
                    duration: duration,
                    durationMinutes: Math.round(duration / 60000 * 100) / 100,
                    averageTimePerTicker: Math.round(duration / testTickers.length)
                }
            });

        } catch (error) {
            console.error("Error in splits batch test:", error);
            addLogJobExecution(LogLevel.ERROR, "test-splits-batch", "Error in splits batch test", {
                error: error instanceof Error ? error.message : String(error)
            });

            return res.status(500).json({
                error: "Internal server error during splits batch test",
                details: error instanceof Error ? error.message : String(error)
            });
        }
    }
}
