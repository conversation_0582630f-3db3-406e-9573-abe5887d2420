import {NextApiRequest, NextApiResponse} from "next";
import {ListOfTickers} from "@/entities/consolidated_data/ListOfTickers";
import {getDividendsController} from "@/controller/dividendsController";
import {checkIfAgendaIsConnected} from "@/lib/utils";

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    // Ensure connection to MongoDB
    try {
        await checkIfAgendaIsConnected();
    } catch (error) {
        return res.status(500).json({error: "Failed to connect to database"});
    }

    // Handle GET request to test batch processing
    if (req.method === "GET") {
        try {
            const {limit = "10", tickers} = req.query;
            
            let testTickers: ListOfTickers[];
            
            if (tickers && typeof tickers === "string") {
                // Use specific tickers if provided
                const tickerCodes = tickers.split(",").map(t => t.trim());
                testTickers = await ListOfTickers.findAll({
                    where: {
                        primary_ticker_eodhd: tickerCodes,
                        is_enable: 1
                    },
                    limit: parseInt(limit as string)
                });
            } else {
                // Get a sample of enabled tickers for testing
                testTickers = await ListOfTickers.findAll({
                    where: {
                        is_enable: 1
                    },
                    limit: parseInt(limit as string),
                    order: [["id", "ASC"]]
                });
            }

            if (testTickers.length === 0) {
                return res.status(404).json({error: "No enabled tickers found for testing"});
            }

            console.log(`Testing dividends batch processing with ${testTickers.length} tickers`);
            const startTime = Date.now();

            await getDividendsController(testTickers);

            const duration = Date.now() - startTime;

            return res.status(200).json({
                success: true,
                testResults: {
                    tickersProcessed: testTickers.length,
                    durationMs: duration,
                    averageTimePerTicker: Math.round(duration / testTickers.length),
                    tickerCodes: testTickers.map(t => t.primary_ticker_eodhd)
                },
                message: "Dividends batch processing test completed successfully"
            });

        } catch (error) {
            console.error("Error testing dividends batch processing:", error);
            return res.status(500).json({
                error: "Failed to test dividends batch processing",
                details: error instanceof Error ? error.message : String(error)
            });
        }
    }

    // Handle unsupported methods
    return res.status(405).json({error: "Method not allowed"});
}
