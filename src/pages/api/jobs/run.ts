import type {NextApiRequest, NextApiResponse} from "next";
import {getAgenda, isJobDefined} from "../../../lib/agenda";
import {checkIfAgendaIsConnected} from "@/lib/utils";
import {ObjectId} from "mongodb";

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    if (req.method !== "POST") {
        return res.status(405).json({error: "Method not allowed"});
    }

    await checkIfAgendaIsConnected();

    const {id, name, data} = req.body;

    if (!id && !name) {
        return res.status(400).json({error: "Job ID or name is required"});
    }

    try {
        const agenda = getAgenda();
        let job;

        if (id) {
            // Run job by ID
            try {
                const objectId = new ObjectId(id);
                const jobs = await agenda.jobs({_id: objectId});

                if (jobs.length === 0) {
                    return res.status(404).json({error: "Job not found"});
                }

                job = jobs[0];
                // Set the force flag to ensure the job runs even if it's not scheduled
                job.attrs.data = {...job.attrs.data, ...data, force: true};
                await job.save();
                //schedule the job
                job.schedule("in 10 seconds");
                await job.save();
                return res.status(200).json({
                    message: "Job started successfully",
                    jobId: job.attrs._id.toString(),
                    name: job.attrs.name,
                    data: job.attrs.data,
                });
            } catch (error) {
                console.error("Error running job by ID:", error);
                return res.status(500).json({error: "Failed to run job"});
            }
        } else if (name) {
            // Run job by name
            try {
                // Check if the job is defined before running it
                if (!isJobDefined(name)) {
                    console.error(`Job "${name}" is not defined`);
                    return res.status(400).json({error: `Undefined job: "${name}" is not defined in the system`});
                }

                // Create a new instance of the job and run it immediately
                // Include the data passed from the request, and add force=true to bypass scheduling checks
                const jobData = {...data, force: true};
                await agenda.now(name, jobData);

                return res.status(200).json({
                    message: "Job started successfully",
                    name,
                    data: jobData,
                });
            } catch (error) {
                console.error("Error running job by name:", error);
                return res.status(500).json({error: "Failed to run job"});
            }
        }
    } catch (error) {
        console.error("Error running job:", error);
        return res.status(500).json({error: "Failed to run job"});
    }
}
