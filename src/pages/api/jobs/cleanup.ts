import type {NextApiRequest, NextApiResponse} from "next";
import {checkIfAgendaIsConnected} from "@/lib/utils";
import {cleanupDuplicateJobs} from "../../../lib/jobDefinitions";

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    if (req.method !== "POST") {
        return res.status(405).json({error: "Method not allowed"});
    }

    await checkIfAgendaIsConnected();

    try {
        // Clean up duplicate jobs
        await cleanupDuplicateJobs();

        return res.status(200).json({
            message: "Job cleanup completed successfully",
        });
    } catch (error) {
        console.error("Error cleaning up jobs:", error);
        return res.status(500).json({error: "Failed to clean up jobs"});
    }
}
