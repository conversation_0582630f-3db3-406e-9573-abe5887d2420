import type {NextApiRequest, NextApiResponse} from "next";
import {getJobsByName, getAgenda} from "../../../lib/agenda";
import {JobDetails} from "@/utils/types/agenda/job";
import {checkIfAgendaIsConnected} from "@/lib/utils";
import {ObjectId} from "mongodb";

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    if (req.method !== "GET") {
        return res.status(405).json({error: "Method not allowed"});
    }

    await checkIfAgendaIsConnected();

    const {name, id} = req.query;

    if ((!name || typeof name !== "string") && (!id || typeof id !== "string")) {
        return res.status(400).json({error: "Job name or id is required"});
    }

    try {
        const agenda = getAgenda();
        let job;

        if (id && typeof id === "string") {
            // Get job by ID - query MongoDB directly to avoid Agenda.js processing
            try {
                const objectId = new ObjectId(id);
                const db = agenda._mdb;
                const collection = db.collection("agendaJobs");

                // Get raw data from MongoDB without Agenda.js processing
                job = await collection.findOne({_id: objectId});
            } catch (error) {
                console.error("Invalid job ID:", error);
                return res.status(400).json({error: "Invalid job ID format"});
            }
        } else if (name && typeof name === "string") {
            // Get jobs by name - also get raw data
            const agenda = getAgenda();
            const db = agenda._mdb;
            const collection = db.collection("agendaJobs");

            // Get raw data from MongoDB
            job = await collection.findOne({name: name});
        }

        if (!job) {
            return res.status(404).json({error: "Job not found"});
        }

        // Format job details using raw data for nextRunAt to avoid Agenda.js processing
        const jobDetails: JobDetails = {
            _id: job._id.toString(),
            name: job.name,
            priority: job.priority,
            type: job.type,
            data: job.data || {},
            lastRunAt: job.lastRunAt,
            lastFinishedAt: job.lastFinishedAt,
            nextRunAt: job.nextRunAt, // Use raw data to get the actual null value
            repeatInterval: job.repeatInterval || "manual",
            disabled: job.disabled,
        };
        return res.status(200).json({job: jobDetails});
    } catch (error) {
        console.error("Error fetching job details:", error);
        return res.status(500).json({error: "Failed to fetch job details"});
    }
}
