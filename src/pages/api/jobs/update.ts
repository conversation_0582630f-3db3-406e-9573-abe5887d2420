import type {NextApiRequest, NextApiResponse} from "next";
import {getAgenda} from "../../../lib/agenda";
import {checkIfAgendaIsConnected} from "@/lib/utils";
import {ObjectId} from "mongodb";

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    if (req.method !== "PUT") {
        return res.status(405).json({error: "Method not allowed"});
    }

    await checkIfAgendaIsConnected();

    const {id, settings} = req.body;

    if (!id) {
        return res.status(400).json({error: "Job ID is required"});
    }

    if (!settings || typeof settings !== "object") {
        return res.status(400).json({error: "Job settings are required"});
    }

    try {
        const agenda = getAgenda();

        // Find the job by ID
        let objectId;
        try {
            objectId = new ObjectId(id);
        } catch (error) {
            console.error("Invalid job ID:", error);
            return res.status(400).json({error: "Invalid job ID format"});
        }

        const jobs = await agenda.jobs({_id: objectId});

        if (jobs.length === 0) {
            return res.status(404).json({error: "Job not found"});
        }

        const job = jobs[0];

        // Update job settings
        const allowedSettings = ["repeatInterval", "disabled", "priority"];
        let hasChanges = false;

        for (const [key, value] of Object.entries(settings)) {
            if (allowedSettings.includes(key)) {
                if (key === "repeatInterval" && value) {
                    job.repeatEvery(value as string);
                    hasChanges = true;
                } else if (key === "disabled") {
                    if (value === true) {
                        job.disable();
                    } else {
                        job.enable();
                    }
                    hasChanges = true;
                } else if (key === "priority" && (typeof value === "number" || typeof value === "string")) {
                    job.priority(String(value));
                    hasChanges = true;
                }
            }
        }

        if (!hasChanges) {
            return res.status(400).json({error: "No valid settings provided"});
        }

        // Save the job
        await job.save();

        // Return the updated job
        return res.status(200).json({
            message: "Job settings updated successfully",
            job: {
                _id: job.attrs._id.toString(),
                name: job.attrs.name,
                priority: job.attrs.priority,
                nextRunAt: job.attrs.nextRunAt,
                type: job.attrs.type,
                data: job.attrs.data || {},
                lastFinishedAt: job.attrs.lastFinishedAt,
                lastRunAt: job.attrs.lastRunAt,
                repeatInterval: job.attrs.repeatInterval || "manual",
                disabled: job.attrs.disabled,
            },
        });
    } catch (error) {
        console.error("Error updating job settings:", error);
        return res.status(500).json({error: "Failed to update job settings"});
    }
}
