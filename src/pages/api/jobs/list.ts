import type {NextApiRequest, NextApiResponse} from "next";
import {getAgenda} from "../../../lib/agenda";
import {checkIfAgendaIsConnected} from "@/lib/utils";

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    if (req.method !== "GET") {
        return res.status(405).json({error: "Method not allowed"});
    }

    await checkIfAgendaIsConnected();

    const db = await getAgenda()._mdb;
    const collection = db.collection("agendaJobs");

    const jobs = await collection.find({}).toArray();

    const jobList = jobs.map((job) => ({
        _id: job._id.toString(),
        name: job.name,
        priority: job.priority,
        nextRunAt: job.nextRunAt ?? null,
        type: job.type === "normal" || job.type === "single" ? job.type : "normal",
        data: job.data || {},
        lastFinishedAt: job.lastFinishedAt,
        lastRunAt: job.lastRunAt,
        repeatInterval: job.repeatInterval || "manual",
        disabled: job.disabled,
    }));

    res.status(200).json(jobList);
}
