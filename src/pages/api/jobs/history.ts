import type {NextApiRequest, NextApiResponse} from "next";
import {getAgenda} from "../../../lib/agenda";
import {checkIfAgendaIsConnected} from "@/lib/utils";
import {ObjectId} from "mongodb";

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    if (req.method !== "GET") {
        return res.status(405).json({error: "Method not allowed"});
    }

    await checkIfAgendaIsConnected();

    const {jobId, page = "1", limit = "10"} = req.query;

    if (!jobId || typeof jobId !== "string") {
        return res.status(400).json({error: "Job ID is required"});
    }

    try {
        const pageNum = parseInt(page as string, 10);
        const limitNum = parseInt(limit as string, 10);
        const skip = (pageNum - 1) * limitNum;

        // Get the job history collection
        const agenda = getAgenda();
        const db = agenda._mdb;
        const historyCollection = db.collection("jobHistory");

        // Query for job history records
        const query = {jobId};

        // Get total count for pagination
        const totalCount = await historyCollection.countDocuments(query);

        // Get paginated history records, sorted by startedAt in descending order (newest first)
        const history = await historyCollection.find(query).sort({startedAt: -1}).skip(skip).limit(limitNum).toArray();

        // Calculate total pages
        const totalPages = Math.ceil(totalCount / limitNum);

        return res.status(200).json({
            history,
            pagination: {
                page: pageNum,
                limit: limitNum,
                totalItems: totalCount,
                totalPages,
            },
        });
    } catch (error) {
        console.error("Error fetching job history:", error);
        return res.status(500).json({error: "Failed to fetch job history"});
    }
}
