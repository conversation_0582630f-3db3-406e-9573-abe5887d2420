import type {NextApiRequest, NextApiResponse} from "next";
import {getAgenda} from "../../../lib/agenda";
import {checkIfAgendaIsConnected} from "@/lib/utils";

interface JobProgressRecord {
    _id: string;
    jobId: string;
    name: string;
    startedAt: string;
    finishedAt?: string;
    duration?: number;
    status: "started" | "completed" | "failed";
    error?: string;
    result?: any;
    totalTickers?: number;
    processedTickers?: number;
    progress?: number;
    lastProgressUpdate?: string;
}

interface RunningJobProgress {
    _id: string;
    name: string;
    progress: number;
    status: "started" | "completed" | "failed";
    startedAt: string;
    totalTickers?: number;
    processedTickers?: number;
    lastProgressUpdate?: string;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    if (req.method !== "GET") {
        return res.status(405).json({error: "Method not allowed"});
    }

    await checkIfAgendaIsConnected();

    try {
        const agenda = getAgenda();
        const db = agenda._mdb;
        const historyCollection = db.collection("jobHistory");

        // Get all currently running jobs (status = "started")
        const runningJobs = await historyCollection
            .find({status: "started"})
            .sort({startedAt: -1})
            .toArray();

        // Format the response
        const formattedJobs: RunningJobProgress[] = runningJobs.map((job) => ({
            _id: job._id.toString(),
            name: job.name,
            progress: job.progress || 0,
            status: job.status,
            startedAt: job.startedAt.toISOString(),
            totalTickers: job.totalTickers,
            processedTickers: job.processedTickers,
            lastProgressUpdate: job.lastProgressUpdate?.toISOString(),
        }));

        return res.status(200).json({
            runningJobs: formattedJobs,
            count: formattedJobs.length,
        });
    } catch (error) {
        console.error("Error fetching job progress:", error);
        return res.status(500).json({error: "Failed to fetch job progress"});
    }
}
