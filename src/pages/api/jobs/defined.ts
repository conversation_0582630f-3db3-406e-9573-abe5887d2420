import type {NextApiRequest, NextApiResponse} from "next";
import {getDefinedJobNames} from "../../../lib/agenda";
import {checkIfAgendaIsConnected} from "@/lib/utils";

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    if (req.method !== "GET") {
        return res.status(405).json({error: "Method not allowed"});
    }

    await checkIfAgendaIsConnected();

    try {
        // Get all defined job names
        const definedJobs = getDefinedJobNames();

        return res.status(200).json({
            definedJobs,
            count: definedJobs.length,
        });
    } catch (error) {
        console.error("Error fetching defined jobs:", error);
        return res.status(500).json({error: "Failed to fetch defined jobs"});
    }
}
