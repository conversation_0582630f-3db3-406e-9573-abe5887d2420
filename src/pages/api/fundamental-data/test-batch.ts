import {NextApiRequest, NextApiResponse} from "next";
import {ListOfTickers} from "../../../entities/consolidated_data/ListOfTickers";
import {getFundamentalDataController} from "../../../controller/fundamentalDataController";

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    // Set CORS headers
    res.setHeader("Access-Control-Allow-Origin", "*");
    res.setHeader("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
    res.setHeader("Access-Control-Allow-Headers", "Content-Type");

    if (req.method === "OPTIONS") {
        return res.status(200).end();
    }

    // Handle GET request to test batch processing
    if (req.method === "GET") {
        try {
            const {limit = "10", tickers} = req.query;
            
            let testTickers: ListOfTickers[];
            
            if (tickers && typeof tickers === "string") {
                // Use specific tickers if provided
                const tickerCodes = tickers.split(",").map(t => t.trim());
                testTickers = await ListOfTickers.findAll({
                    where: {
                        primary_ticker_eodhd: tickerCodes,
                        is_enable: 1
                    },
                    limit: parseInt(limit as string)
                });
            } else {
                // Get a sample of enabled tickers for testing
                testTickers = await ListOfTickers.findAll({
                    where: {
                        is_enable: 1
                    },
                    limit: parseInt(limit as string),
                    order: [["id", "ASC"]]
                });
            }

            if (testTickers.length === 0) {
                return res.status(404).json({error: "No enabled tickers found for testing"});
            }

            console.log(`Testing batch processing with ${testTickers.length} tickers`);
            const startTime = Date.now();

            await getFundamentalDataController(testTickers);

            const duration = Date.now() - startTime;

            return res.status(200).json({
                success: true,
                testResults: {
                    tickersProcessed: testTickers.length,
                    durationMs: duration,
                    averageTimePerTicker: Math.round(duration / testTickers.length),
                    tickerCodes: testTickers.map(t => t.primary_ticker_eodhd)
                },
                message: "Batch processing completed successfully"
            });

        } catch (error: any) {
            console.error("Error in fundamental data batch test:", error);
            return res.status(500).json({
                error: "Failed to test batch processing",
                details: error.message
            });
        }
    }

    return res.status(405).json({error: "Method not allowed"});
}
