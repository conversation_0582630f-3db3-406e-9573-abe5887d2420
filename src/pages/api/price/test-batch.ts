import {NextApiRequest, NextApiResponse} from "next";
import {PriceController} from "@/controller/priceController";
import {ListOfTickers} from "@/entities/consolidated_data/ListOfTickers";
import {checkIfAgendaIsConnected} from "@/lib/utils";

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    // Ensure connection to MongoDB
    try {
        await checkIfAgendaIsConnected();
    } catch (error) {
        return res.status(500).json({error: "Failed to connect to database"});
    }

    // Handle GET request to test batch processing
    if (req.method === "GET") {
        try {
            const {limit = "10", tickers} = req.query;
            
            let testTickers: ListOfTickers[];
            
            if (tickers && typeof tickers === "string") {
                // Use specific tickers if provided
                const tickerCodes = tickers.split(",").map(t => t.trim());
                testTickers = await ListOfTickers.findAll({
                    where: {
                        primary_ticker_eodhd: tickerCodes,
                        is_enable: 1
                    },
                    limit: parseInt(limit as string)
                });
            } else {
                // Get a sample of enabled tickers for testing
                testTickers = await ListOfTickers.findAll({
                    where: {
                        is_enable: 1
                    },
                    limit: parseInt(limit as string),
                    order: [["id", "ASC"]]
                });
            }

            if (testTickers.length === 0) {
                return res.status(404).json({error: "No enabled tickers found for testing"});
            }

            console.log(`Testing batch processing with ${testTickers.length} tickers`);
            const startTime = Date.now();

            const priceController = new PriceController();
            const [validPrices, invalidPrices] = await priceController.getPrices(testTickers);

            const duration = Date.now() - startTime;

            return res.status(200).json({
                success: true,
                testResults: {
                    tickersProcessed: testTickers.length,
                    validPrices: validPrices.length,
                    invalidPrices: invalidPrices.length,
                    durationMs: duration,
                    averageTimePerTicker: Math.round(duration / testTickers.length),
                    tickerCodes: testTickers.map(t => t.primary_ticker_eodhd)
                },
                validPrices: validPrices.slice(0, 5), // Show first 5 for debugging
                invalidPrices: invalidPrices.slice(0, 5) // Show first 5 for debugging
            });

        } catch (error: any) {
            console.error("Error testing batch processing:", error);
            return res.status(500).json({
                error: "Failed to test batch processing",
                details: error.message
            });
        }
    }

    return res.status(405).json({error: "Method not allowed"});
}
