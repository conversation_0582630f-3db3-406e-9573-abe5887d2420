import {NextApiRequest, NextApiResponse} from "next";
import {getAgenda} from "@/lib/agenda";
import {checkIfAgendaIsConnected} from "@/lib/utils";
import {ObjectId} from "mongodb";

// Get the settings collection
const getAgendaSettingsCollection = () => {
    const agenda = getAgenda();
    const db = agenda._mdb;
    return db.collection("agendaSettings");
};

// Get or create agenda settings
const getOrCreateAgendaSettings = async () => {
    try {
        const settingsId = new ObjectId("00000001c1c580362ac42646"); // Hardcoded ID as in other parts of the codebase
        const settingsCollection = getAgendaSettingsCollection();

        // Check if settings document exists
        let existingSettings = await settingsCollection.findOne({_id: settingsId});
        if (existingSettings) {
            return existingSettings;
        }

        // Create new document with default values
        await settingsCollection.insertOne({
            _id: settingsId,
            agendaStartDateTime: new Date(),
            checkAgendaStatusInterval: 30, // Default value in minutes
            priceBatchSize: 30, // Default batch size for price API calls
            priceChunkSize: 1000, // Default chunk size for database operations
            dividendsBatchSize: 10, // Default batch size for dividends API calls
            fundamentalDataBatchSize: 10, // Default batch size for fundamental data API calls
            processS3FilesBatchSize: 10, // Default batch size for S3 files processing
            splitsBatchSize: 10, // Default batch size for splits API calls
            epsBatchSize: 10, // Default batch size for EPS processing
        });

        existingSettings = await settingsCollection.findOne({_id: settingsId});
        console.log("Created agendaSettings with default values");
        return existingSettings;
    } catch (error) {
        console.error("Error getting/creating agendaSettings:", error);
        throw error;
    }
};

// Update agenda settings
const updateAgendaSettings = async (settings: any) => {
    try {
        const settingsId = new ObjectId("00000001c1c580362ac42646");
        const settingsCollection = getAgendaSettingsCollection();

        // Get existing settings
        const existingSettings = await settingsCollection.findOne({_id: settingsId});
        if (!existingSettings) {
            return await getOrCreateAgendaSettings();
        }

        // Update settings
        const updatedSettings = {...existingSettings, ...settings};
        await settingsCollection.replaceOne({_id: settingsId}, updatedSettings);

        return updatedSettings;
    } catch (error) {
        console.error("Error updating agendaSettings:", error);
        throw error;
    }
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    // Ensure connection to MongoDB
    try {
        await checkIfAgendaIsConnected();
    } catch (error) {
        return res.status(500).json({error: "Failed to connect to database"});
    }

    // Handle GET request
    if (req.method === "GET") {
        try {
            const settings = await getOrCreateAgendaSettings();
            return res.status(200).json(settings);
        } catch (error) {
            console.error("Error fetching agenda settings:", error);
            return res.status(500).json({error: "Failed to fetch agenda settings"});
        }
    }

    // Handle PUT request
    if (req.method === "PUT") {
        try {
            const {checkAgendaStatusInterval, priceBatchSize, priceChunkSize, dividendsBatchSize, fundamentalDataBatchSize, processS3FilesBatchSize, splitsBatchSize, epsBatchSize} = req.body;
            const updateData: any = {};

            // Validate checkAgendaStatusInterval
            if (checkAgendaStatusInterval !== undefined) {
                const interval = Number(checkAgendaStatusInterval);
                if (isNaN(interval) || interval <= 0) {
                    return res.status(400).json({error: "checkAgendaStatusInterval must be a positive number"});
                }
                updateData.checkAgendaStatusInterval = interval;
            }

            // Validate priceBatchSize
            if (priceBatchSize !== undefined) {
                const batchSize = Number(priceBatchSize);
                if (isNaN(batchSize) || batchSize <= 0 || batchSize > 100) {
                    return res.status(400).json({error: "priceBatchSize must be a positive number between 1 and 100"});
                }
                updateData.priceBatchSize = batchSize;
            }

            // Validate priceChunkSize
            if (priceChunkSize !== undefined) {
                const chunkSize = Number(priceChunkSize);
                if (isNaN(chunkSize) || chunkSize <= 0 || chunkSize > 5000) {
                    return res.status(400).json({error: "priceChunkSize must be a positive number between 1 and 5000"});
                }
                updateData.priceChunkSize = chunkSize;
            }

            // Validate dividendsBatchSize
            if (dividendsBatchSize !== undefined) {
                const batchSize = Number(dividendsBatchSize);
                if (isNaN(batchSize) || batchSize <= 0 || batchSize > 50) {
                    return res.status(400).json({error: "dividendsBatchSize must be a positive number between 1 and 50"});
                }
                updateData.dividendsBatchSize = batchSize;
            }

            // Validate fundamentalDataBatchSize
            if (fundamentalDataBatchSize !== undefined) {
                const batchSize = Number(fundamentalDataBatchSize);
                if (isNaN(batchSize) || batchSize <= 0 || batchSize > 50) {
                    return res.status(400).json({error: "fundamentalDataBatchSize must be a positive number between 1 and 50"});
                }
                updateData.fundamentalDataBatchSize = batchSize;
            }

            // Validate processS3FilesBatchSize
            if (processS3FilesBatchSize !== undefined) {
                const batchSize = Number(processS3FilesBatchSize);
                if (isNaN(batchSize) || batchSize <= 0 || batchSize > 50) {
                    return res.status(400).json({error: "processS3FilesBatchSize must be a positive number between 1 and 50"});
                }
                updateData.processS3FilesBatchSize = batchSize;
            }

            // Validate splitsBatchSize
            if (splitsBatchSize !== undefined) {
                const batchSize = Number(splitsBatchSize);
                if (isNaN(batchSize) || batchSize <= 0 || batchSize > 50) {
                    return res.status(400).json({error: "splitsBatchSize must be a positive number between 1 and 50"});
                }
                updateData.splitsBatchSize = batchSize;
            }

            // Validate epsBatchSize
            if (epsBatchSize !== undefined) {
                const batchSize = Number(epsBatchSize);
                if (isNaN(batchSize) || batchSize <= 0 || batchSize > 50) {
                    return res.status(400).json({error: "epsBatchSize must be a positive number between 1 and 50"});
                }
                updateData.epsBatchSize = batchSize;
            }

            // Check if any valid settings were provided
            if (Object.keys(updateData).length === 0) {
                return res.status(400).json({error: "No valid settings provided"});
            }

            const updatedSettings = await updateAgendaSettings(updateData);
            return res.status(200).json(updatedSettings);
        } catch (error) {
            console.error("Error updating agenda settings:", error);
            return res.status(500).json({error: "Failed to update agenda settings"});
        }
    }

    // Handle unsupported methods
    return res.status(405).json({error: "Method not allowed"});
}
