import {isAgendaConnected, getAgendaStartTime} from "@/lib/agenda";
import {checkIfAgendaIsConnected} from "@/lib/utils";
import {NextApiRequest, NextApiResponse} from "next";

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    if (req.method !== "GET") {
        return res.status(405).json({error: "Method not allowed"});
    }
    try {
        await checkIfAgendaIsConnected();
        const connected = isAgendaConnected();
        const startTime = await getAgendaStartTime();

        // Calculate uptime if connected and startTime exists
        let uptime = null;
        if (connected && startTime) {
            const uptimeMs = Date.now() - startTime.getTime();
            const uptimeSec = Math.floor(uptimeMs / 1000);
            const days = Math.floor(uptimeSec / 86400);
            const hours = Math.floor((uptimeSec % 86400) / 3600);
            const minutes = Math.floor((uptimeSec % 3600) / 60);
            uptime = `${days}d ${hours}h ${minutes}m`;
        }

        return res.status(200).json({
            connected,
            status: connected ? "online" : "offline",
            startTime: startTime ? startTime.toISOString() : null,
            uptime,
        });
    } catch (error) {
        console.error("Error checking Agenda connection:", error);
        return res.status(500).json({connected: false, status: "error", error: "Failed to check connection"});
    }
}
