import {connectAgendaToMongoDB, isAgendaConnected} from "@/lib/agenda";
import dotenv from "dotenv";

dotenv.config();

const {LOG_LEVEL} = process.env;

export const checkIfAgendaIsConnected = async () => {
    // Ensure MongoDB connection is established first
    if (!isAgendaConnected()) {
        await connectAgendaToMongoDB();

        // If still not connected, return appropriate error
        if (!isAgendaConnected()) {
            throw new Error("Database connection not available");
        }
    }
};

export const consoleLog = (message: string, data?: any) => {
    if (LOG_LEVEL === "DEBUG") {
        console.log(message, data);
    }
};
