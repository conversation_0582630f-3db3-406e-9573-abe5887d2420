import { cn } from "@/lib/client-utils"
import styles from "./status-card.module.css"
import { StatusCardProps } from "@/utils/types/agenda/status"

export function StatusCard({ title, value, description, status }: StatusCardProps) {
  return (
    <div className={styles.card}>
      <div className={styles.header}>
        <h3 className={styles.title}>{title}</h3>
        <div
          className={cn(
            styles.indicator,
            status === "online" && styles.online,
            status === "offline" && styles.offline,
            status === "warning" && styles.warning,
            status === "error" && styles.error,
            status === "info" && styles.info,
          )}
        />
      </div>
      <div className={styles.content}>
        <div className={styles.value}>{value}</div>
        <p className={styles.description}>{description}</p>
      </div>
    </div>
  )
}
