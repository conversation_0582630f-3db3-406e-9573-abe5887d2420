"use client";

import {useState, useEffect} from "react";
import styles from "./job-progress-card.module.css";
import axios from "axios";

interface RunningJob {
    _id: string;
    name: string;
    progress: number;
    status: "started" | "completed" | "failed";
    startedAt: string;
    totalTickers?: number;
    processedTickers?: number;
    lastProgressUpdate?: string;
}

interface JobProgressResponse {
    runningJobs: RunningJob[];
    count: number;
}

export function JobProgressCard() {
    const [runningJobs, setRunningJobs] = useState<RunningJob[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    const fetchRunningJobs = async () => {
        try {
            const response = await axios.get<JobProgressResponse>("/api/jobs/progress");
            setRunningJobs(response.data.runningJobs);
            setError(null);
        } catch (error) {
            console.error("Error fetching running jobs:", error);
            setError("Failed to fetch running jobs");
        } finally {
            setLoading(false);
        }
    };

    // Helper function to format relative time
    const formatRelativeTime = (dateString: string): string => {
        const date = new Date(dateString);
        const now = new Date();
        const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

        if (diffInMinutes < 1) return "Just now";
        if (diffInMinutes === 1) return "1 minute ago";
        if (diffInMinutes < 60) return `${diffInMinutes} minutes ago`;

        const diffInHours = Math.floor(diffInMinutes / 60);
        if (diffInHours === 1) return "1 hour ago";
        if (diffInHours < 24) return `${diffInHours} hours ago`;

        const diffInDays = Math.floor(diffInHours / 24);
        if (diffInDays === 1) return "1 day ago";
        return `${diffInDays} days ago`;
    };

    useEffect(() => {
        fetchRunningJobs();

        // Set up polling every minute
        const interval = setInterval(fetchRunningJobs, 60000);

        return () => clearInterval(interval);
    }, []);
    if (loading) {
        return (
            <div className={styles.card}>
                <div className={styles.header}>
                    <h3 className={styles.title}>Running Jobs</h3>
                    <div className={styles.badge}>Loading...</div>
                </div>
                <div className={styles.content}>
                    <p className={styles.emptyState}>Loading running jobs...</p>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className={styles.card}>
                <div className={styles.header}>
                    <h3 className={styles.title}>Running Jobs</h3>
                    <div className={styles.badge}>Error</div>
                </div>
                <div className={styles.content}>
                    <p className={styles.emptyState}>{error}</p>
                </div>
            </div>
        );
    }

    return (
        <div className={styles.card}>
            <div className={styles.header}>
                <h3 className={styles.title}>Running Jobs</h3>
                <div className={styles.badge}>{runningJobs.length} Active</div>
            </div>
            <div className={styles.content}>
                {runningJobs.length === 0 ? (
                    <p className={styles.emptyState}>No jobs currently running</p>
                ) : (
                    <div className={styles.jobsList}>
                        {runningJobs.map((job) => (
                            <div key={job._id} className={styles.jobItem}>
                                <div className={styles.jobInfo}>
                                    <p className={styles.jobName}>{job.name}</p>
                                    <p className={styles.jobTime}>Started {formatRelativeTime(job.startedAt)}</p>
                                    {job.totalTickers && (
                                        <p className={styles.jobTime}>
                                            {job.processedTickers || 0} / {job.totalTickers} tickers
                                        </p>
                                    )}
                                </div>
                                <div className={styles.progressSection}>
                                    <div className={styles.progressContainer}>
                                        <div className={styles.progressBar} style={{width: `${job.progress}%`}} />
                                    </div>
                                    <span
                                        className={styles.progressInnerText}
                                        style={{
                                            color: job.progress > 45 ? "var(--background)" : "var(--foreground)",
                                            opacity: job.progress > 10 ? 1 : 0,
                                        }}>
                                        {job.progress}%
                                    </span>
                                </div>
                            </div>
                        ))}
                    </div>
                )}
            </div>
        </div>
    );
}
