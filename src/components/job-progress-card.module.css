.card {
    background-color: var(--card);
    border-radius: 0.5rem;
    border: 1px solid var(--border);
    overflow: hidden;
    height: 100%;
}

.header {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 1rem 0.5rem 1rem;
}

.title {
    font-size: 0.875rem;
    font-weight: 500;
}

.badge {
    display: inline-flex;
    align-items: center;
    border-radius: 9999px;
    padding: 0.25rem 0.75rem;
    font-size: 0.75rem;
    font-weight: 500;
    background-color: var(--primary-light);
    color: var(--primary);
}

.content {
    padding: 0 1rem 1rem 1rem;
}

.emptyState {
    color: var(--muted-foreground);
    font-size: 0.875rem;
    text-align: center;
    padding: 2rem 0;
}

.jobsList {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.jobItem {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.jobInfo {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.jobName {
    font-size: 0.875rem;
    font-weight: 500;
}

.jobTime {
    font-size: 0.75rem;
    color: var(--muted-foreground);
}

.progressSection {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.progressContainer {
    flex: 1;
    height: 8px;
    background-color: var(--muted);
    border-radius: 4px;
    overflow: hidden;
    position: relative;
}

.progressBar {
    height: 100%;
    background-color: var(--primary);
    border-radius: 4px;
    transition: width 0.3s ease;
}

.progressText {
    font-size: 0.75rem;
    font-weight: 500;
    min-width: 2.5rem;
    text-align: right;
}

.progressInnerText {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 0.75rem;
    font-weight: 500;
    transition: color 0.3s ease;
    white-space: nowrap;
}
