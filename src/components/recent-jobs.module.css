.container {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.job {
    display: flex;
    align-items: center;
}

.avatar {
    height: 2.25rem;
    width: 2.25rem;
    border-radius: 9999px;
    overflow: hidden;
}

.avatarFallback {
    height: 100%;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--primary-light);
    color: var(--primary);
    font-weight: 500;
}

.info {
    margin-left: 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.name {
    font-size: 0.875rem;
    font-weight: 500;
}

.time {
    font-size: 0.875rem;
    color: var(--muted-foreground);
}

.status {
    margin-left: auto;
}

.badge {
    display: inline-flex;
    align-items: center;
    border-radius: 9999px;
    padding: 0.25rem 0.75rem;
    font-size: 0.75rem;
    font-weight: 500;
}

.completed {
    background-color: var(--primary-light);
    color: var(--primary);
}

.failed {
    background-color: var(--destructive-light);
    color: var(--destructive);
}
