.card {
    background-color: var(--card);
    border-radius: 0.5rem;
    border: 1px solid var(--border);
    overflow: hidden;
    height: 100%;
}

.header {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 1rem 0.5rem 1rem;
}

.title {
    font-size: 0.875rem;
    font-weight: 500;
}

.badge {
    display: inline-flex;
    align-items: center;
    border-radius: 9999px;
    padding: 0.25rem 0.75rem;
    font-size: 0.75rem;
    font-weight: 500;
    color: white;
}

.content {
    padding: 0 1rem 1rem 1rem;
}

.usageText {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.usedCredits {
    font-weight: 700;
}

.separator {
    margin: 0 0.25rem;
    color: var(--muted-foreground);
}

.totalCredits {
    color: var(--muted-foreground);
}

.progressBarContainer {
    height: 0.5rem;
    background-color: var(--muted);
    border-radius: 9999px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.progressBar {
    height: 100%;
    border-radius: 9999px;
    transition: width 0.3s ease;
}

.remainingText {
    font-size: 0.875rem;
    color: var(--muted-foreground);
    margin-top: 0.5rem;
}
