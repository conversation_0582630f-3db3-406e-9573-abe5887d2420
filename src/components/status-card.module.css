.card {
    background-color: var(--card);
    border-radius: 0.5rem;
    border: 1px solid var(--border);
    overflow: hidden;
}

.header {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 1rem 0.5rem 1rem;
}

.title {
    font-size: 0.875rem;
    font-weight: 500;
}

.indicator {
    height: 0.5rem;
    width: 0.5rem;
    border-radius: 9999px;
}

.online {
    background-color: #10b981;
}

.offline {
    background-color: #6b7280;
}

.warning {
    background-color: #f59e0b;
}

.error {
    background-color: #ef4444;
}

.info {
    background-color: #3b82f6;
}

.content {
    padding: 0 1rem 1rem 1rem;
}

.value {
    font-size: 1.5rem;
    font-weight: 700;
}

.description {
    font-size: 0.75rem;
    color: var(--muted-foreground);
}
