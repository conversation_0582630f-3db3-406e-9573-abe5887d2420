import styles from "./recent-jobs.module.css"

// Mock data for recent jobs
const recentJobs = [
  {
    id: "1",
    name: "send-email",
    status: "completed",
    time: "2 minutes ago",
  },
  {
    id: "2",
    name: "process-payment",
    status: "completed",
    time: "15 minutes ago",
  },
  {
    id: "3",
    name: "generate-report",
    status: "failed",
    time: "1 hour ago",
  },
  {
    id: "4",
    name: "sync-data",
    status: "completed",
    time: "3 hours ago",
  },
  {
    id: "5",
    name: "backup-database",
    status: "completed",
    time: "5 hours ago",
  },
]

export function RecentJobs() {
  return (
    <div className={styles.container}>
      {recentJobs.map((job) => (
        <div key={job.id} className={styles.job}>
          <div className={styles.avatar}>
            <div className={styles.avatarFallback}>{job.name.substring(0, 2).toUpperCase()}</div>
          </div>
          <div className={styles.info}>
            <p className={styles.name}>{job.name}</p>
            <p className={styles.time}>{job.time}</p>
          </div>
          <div className={styles.status}>
            <span className={`${styles.badge} ${job.status === "completed" ? styles.completed : styles.failed}`}>
              {job.status}
            </span>
          </div>
        </div>
      ))}
    </div>
  )
}
