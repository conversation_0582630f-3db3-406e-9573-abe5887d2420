"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "recharts"
import styles from "./jobs-overview.module.css"

// Mock data for the chart
const data = [
  {
    name: "<PERSON>",
    completed: 12,
    failed: 2,
  },
  {
    name: "<PERSON><PERSON>",
    completed: 15,
    failed: 1,
  },
  {
    name: "We<PERSON>",
    completed: 18,
    failed: 0,
  },
  {
    name: "<PERSON>hu",
    completed: 14,
    failed: 3,
  },
  {
    name: "<PERSON><PERSON>",
    completed: 20,
    failed: 1,
  },
  {
    name: "<PERSON><PERSON>",
    completed: 8,
    failed: 0,
  },
  {
    name: "<PERSON>",
    completed: 6,
    failed: 1,
  },
]

export function JobsOverview() {
  return (
    <div className={styles.container}>
      <ResponsiveContainer width="100%" height={350}>
        <BarChart data={data}>
          <XAxis dataKey="name" stroke="var(--muted-foreground)" fontSize={12} tickLine={false} axisLine={false} />
          <YAxis stroke="var(--muted-foreground)" fontSize={12} tickLine={false} axisLine={false} />
          <Tooltip
            contentStyle={{
              backgroundColor: "var(--card)",
              border: "1px solid var(--border)",
              borderRadius: "0.25rem",
              color: "var(--foreground)",
            }}
          />
          <Bar dataKey="completed" fill="var(--success)" radius={[4, 4, 0, 0]} />
          <Bar dataKey="failed" fill="var(--destructive)" radius={[4, 4, 0, 0]} />
        </BarChart>
      </ResponsiveContainer>
    </div>
  )
}
