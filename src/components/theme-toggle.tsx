"use client"

import { Moon, Sun } from "lucide-react"
import { useTheme } from "@/components/theme-provider"
import { Button } from "@/components/ui/button"
import styles from "./theme-toggle.module.css"

export function ThemeToggle() {
  const { theme, setTheme } = useTheme()

  return (
    <Button
      variant="ghost"
      size="icon"
      onClick={() => setTheme(theme === "light" ? "dark" : "light")}
      className={styles.themeToggle}
    >
      <Sun className={styles.sunIcon} />
      <Moon className={styles.moonIcon} />
      <span className="sr-only">Toggle theme</span>
    </Button>
  )
}
