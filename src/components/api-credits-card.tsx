import styles from "./api-credits-card.module.css";

interface ApiCreditsCardProps {
    usedToday: number;
    totalLimit: number;
    className?: string;
}

export function ApiCreditsCard({usedToday, totalLimit, className}: ApiCreditsCardProps) {
    const percentUsed = Math.round((usedToday / totalLimit) * 100);
    const remaining = totalLimit - usedToday;

    // Determine status color based on usage percentage
    let statusColor = "var(--success)";
    if (percentUsed > 90) {
        statusColor = "var(--destructive)";
    } else if (percentUsed > 70) {
        statusColor = "var(--warning, #f59e0b)";
    }

    return (
        <div className={`${styles.card} ${className}`}>
            <div className={styles.header}>
                <h3 className={styles.title}>API Credits</h3>
                <div className={styles.badge} style={{backgroundColor: statusColor}}>
                    {percentUsed}% Used
                </div>
            </div>
            <div className={styles.content}>
                <div className={styles.usageText}>
                    <span className={styles.usedCredits}>{usedToday.toLocaleString()}</span>
                    <span className={styles.separator}>/</span>
                    <span className={styles.totalCredits}>{totalLimit.toLocaleString()}</span>
                </div>
                <div className={styles.progressBarContainer}>
                    <div
                        className={styles.progressBar}
                        style={{
                            width: `${percentUsed}%`,
                            backgroundColor: statusColor,
                        }}></div>
                </div>
                <p className={styles.remainingText}>{remaining.toLocaleString()} credits remaining today</p>
            </div>
        </div>
    );
}
