.sidebar {
    display: flex;
    flex-direction: column;
    width: 16rem;
    border-right: 1px solid var(--border);
    background-color: var(--background);
    height: 100vh;
}

.header {
    padding: 1.5rem;
}

.title {
    font-size: 1.25rem;
    font-weight: 700;
}

.nav {
    flex: 1;
    padding: 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.navLink {
    display: flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    text-decoration: none;
    color: var(--foreground);
    transition: background-color 0.2s, color 0.2s;
}

.navLink:hover {
    background-color: var(--accent);
}

.activeNavLink {
    background-color: var(--accent);
    font-weight: 500;
}

.navIcon {
    margin-right: 0.5rem;
    height: 1rem;
    width: 1rem;
}

.footer {
    padding: 1rem;
    border-top: 1px solid var(--border);
}

.status {
    display: flex;
    align-items: center;
}

.statusIndicator {
    width: 0.5rem;
    height: 0.5rem;
    border-radius: 9999px;
    background-color: #10b981;
    margin-right: 0.5rem;
}

.statusText {
    font-size: 0.875rem;
}
