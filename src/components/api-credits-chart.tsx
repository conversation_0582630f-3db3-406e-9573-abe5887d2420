"use client";

import {useState, useEffect} from "react";
import {<PERSON>, <PERSON><PERSON><PERSON>, ResponsiveContainer, XAxis, YAxis, CartesianGrid, <PERSON>ltip, <PERSON>} from "recharts";
import {Button} from "@/components/ui/button";
import styles from "./api-credits-chart.module.css";

// Types for API response
interface ApiCreditData {
    date: string;
    usage: number;
    formattedDate: string;
}

interface ApiStatisticsResponse {
    todayApiCredits: number;
    historicalData: Array<{
        date: string;
        usage: number;
    }>;
    days: number;
    api: string;
}

// Helper function to format date for display
const formatDateForDisplay = (dateString: string): string => {
    // Parse the date string as local date to avoid timezone issues
    const [year, month, day] = dateString.split("-").map(Number);
    const date = new Date(year, month - 1, day); // month is 0-indexed
    return date.toLocaleDateString("en-US", {month: "short", day: "numeric"});
};

// Helper function to fill missing dates with zero usage
const fillMissingDates = (data: Array<{date: string; usage: number}>, days: number): ApiCreditData[] => {
    const result: ApiCreditData[] = [];
    const today = new Date();

    for (let i = days - 1; i >= 0; i--) {
        const date = new Date(today);
        date.setDate(date.getDate() - i);
        // Use local date string to avoid timezone issues
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const day = String(date.getDate()).padStart(2, "0");
        const dateString = `${year}-${month}-${day}`;

        const existingData = data.find((item) => item.date === dateString);
        const usage = existingData ? Number(existingData.usage) : 0;

        result.push({
            date: dateString,
            usage,
            formattedDate: formatDateForDisplay(dateString),
        });
    }

    return result;
};

interface ApiCreditsChartProps {
    className?: string;
}

export function ApiCreditsChart({className}: ApiCreditsChartProps) {
    const [timeRange, setTimeRange] = useState<"7days" | "30days">("30days");
    const [data, setData] = useState<ApiCreditData[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    // Fetch API credits data
    const fetchApiCreditsData = async (days: number) => {
        try {
            setLoading(true);
            setError(null);

            const response = await fetch(`/api/eod-api/statistics?days=${days}&api=eodhd`);
            console.log("response", response);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result: ApiStatisticsResponse = await response.json();

            // Fill missing dates with zero usage
            const filledData = fillMissingDates(result.historicalData || [], days);
            setData(filledData);
        } catch (err) {
            console.error("Error fetching API credits data:", err);
            setError("Failed to load API credits data");
            // Fallback to empty data
            setData([]);
        } finally {
            setLoading(false);
        }
    };

    // Fetch data when component mounts or timeRange changes
    useEffect(() => {
        const days = timeRange === "7days" ? 7 : 30;
        fetchApiCreditsData(days);
    }, [timeRange]);

    return (
        <div className={`${styles.container} ${className}`}>
            <div className={styles.header}>
                <h3 className={styles.title}>API Credits Usage</h3>
                <div className={styles.timeRangeSelector}>
                    <Button variant={timeRange === "7days" ? "default" : "outline"} size="sm" onClick={() => setTimeRange("7days")} className={styles.timeRangeButton}>
                        Past 7 Days
                    </Button>
                    <Button variant={timeRange === "30days" ? "default" : "outline"} size="sm" onClick={() => setTimeRange("30days")} className={styles.timeRangeButton}>
                        Past 30 Days
                    </Button>
                </div>
            </div>
            <div className={styles.chartContainer}>
                {loading ? (
                    <div style={{display: "flex", justifyContent: "center", alignItems: "center", height: "300px"}}>
                        <p>Loading API credits data...</p>
                    </div>
                ) : error ? (
                    <div style={{display: "flex", justifyContent: "center", alignItems: "center", height: "300px"}}>
                        <p style={{color: "var(--destructive)"}}>{error}</p>
                    </div>
                ) : (
                    <ResponsiveContainer width="100%" height={300}>
                        <BarChart data={data}>
                            <CartesianGrid strokeDasharray="3 3" stroke="var(--border)" />
                            <XAxis
                                dataKey="formattedDate"
                                stroke="var(--muted-foreground)"
                                fontSize={12}
                                tickLine={false}
                                axisLine={{stroke: "var(--border)"}}
                                tick={{fill: "var(--muted-foreground)"}}
                            />
                            <YAxis
                                stroke="var(--muted-foreground)"
                                fontSize={12}
                                tickLine={false}
                                axisLine={{stroke: "var(--border)"}}
                                tick={{fill: "var(--muted-foreground)"}}
                                tickFormatter={(value) => `${value.toLocaleString()}`}
                            />
                            <Tooltip
                                contentStyle={{
                                    backgroundColor: "var(--card)",
                                    border: "1px solid var(--border)",
                                    borderRadius: "0.25rem",
                                    color: "var(--foreground)",
                                }}
                                formatter={(value: number) => [`${value.toLocaleString()} credits`, "Usage"]}
                                labelFormatter={(label) => `Date: ${label}`}
                            />
                            <Legend />
                            <Bar dataKey="usage" fill="var(--primary)" name="API Credits" barSize={10} radius={[4, 4, 0, 0]} />
                        </BarChart>
                    </ResponsiveContainer>
                )}
            </div>
            <div className={styles.footer}>
                <p className={styles.footerText}>Daily limit: 100,000 credits</p>
            </div>
        </div>
    );
}
