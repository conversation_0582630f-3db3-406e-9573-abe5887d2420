.container {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.title {
    font-size: 1.25rem;
    font-weight: 600;
}

.timeRangeSelector {
    display: flex;
    gap: 0.5rem;
}

.timeRangeButton {
    font-size: 0.875rem;
}

.chartContainer {
    flex: 1;
    width: 100%;
}

.footer {
    margin-top: 1rem;
    text-align: right;
}

.footerText {
    font-size: 0.875rem;
    color: var(--muted-foreground);
}
