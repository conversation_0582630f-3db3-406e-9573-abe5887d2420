import {FilesToIncomeStatementExecute} from "../execute/filesToIncomeStatement";
import {LogsOfTickersExecute} from "../execute/logsOfTickersExecute";
import {IncomeStatementRepository} from "../repositories/implements/IncomeStatementRepository";
import {LogsController} from "./logsController";

export class IncomeStatementController {
    async parseData(ticker_internal_id: number, file: any) {
        try {
            // cashFlow
            const incomeStatementRepository = new IncomeStatementRepository(ticker_internal_id);

            const filesToCashFlow = new FilesToIncomeStatementExecute(incomeStatementRepository, file);

            return await filesToCashFlow.execute();
        } catch (error: any) {
            console.log("Income Statement", error.message);
            await LogsController.saveError(ticker_internal_id, error.message);
        }
    }
}
