import {isNull} from "lodash";
import {Op} from "sequelize";
import {BalanceSheet} from "../entities/consolidated_data/BalanceSheet";
import {CashFlow} from "../entities/consolidated_data/CashFlow";
import {Document_type_year_or_quarter} from "../entities/consolidated_data/DocumentTypeYearorQuarterly";
import {IncomeStatement} from "../entities/consolidated_data/IncomeStatement";
import {ListOfTickers} from "../entities/consolidated_data/ListOfTickers";
import {addLogJobExecution} from "@/lib/agenda";
import {LogLevel} from "@/utils/types/logs/log";
import {getEpsBatchSize} from "../lib/agenda";
import {initializeJobProgress, updateJobProgress} from "../lib/jobDefinitions";
import {ObjectId} from "mongodb";

export class EPSController {
    tickers: ListOfTickers[];

    async setEPS(data: ListOfTickers[], historyId?: string) {
        try {
            addLogJobExecution(LogLevel.INFO, "setEPS", "Setting EPS for tickers", {tickersLength: data.length});

            if (data.length === 0) {
                return;
            }

            this.tickers = data;
            console.log("Quantidade tickers", this.tickers.length);

            // Get configurable batch size
            const batchSize = await getEpsBatchSize();
            console.log(`Processing ${data.length} tickers with batch size: ${batchSize}`);

            addLogJobExecution(LogLevel.INFO, "setEPS", "Starting batch EPS processing", {
                totalTickers: data.length,
                batchSize: batchSize,
            });

            // Initialize progress tracking if historyId is provided
            if (historyId) {
                try {
                    await initializeJobProgress(new ObjectId(historyId), data.length);
                } catch (error) {
                    console.error("Error initializing job progress:", error);
                }
            }

            // Process tickers in batches
            const batches = createBatchesForEPS(data, batchSize);

            // Determine if we should process batches concurrently or sequentially
            const maxConcurrentBatches = Math.min(3, batches.length); // Limit concurrent batches for database operations
            const shouldProcessConcurrently = batches.length > 1 && maxConcurrentBatches > 1;

            if (shouldProcessConcurrently) {
                console.log(`Processing ${batches.length} batches with max ${maxConcurrentBatches} concurrent batches`);
                await this.processBatchesConcurrentlyForEPS(batches, maxConcurrentBatches, historyId);
            } else {
                console.log(`Processing ${batches.length} batches sequentially`);
                await this.processBatchesSequentiallyForEPS(batches, historyId);
            }

            addLogJobExecution(LogLevel.INFO, "setEPS", "EPS processing completed", {
                totalTickers: data.length,
                batchesProcessed: batches.length,
            });
        } catch (error) {
            addLogJobExecution(LogLevel.ERROR, "setEPS", "Error when try to set EPS", {error: error instanceof Error ? error.message : String(error)});
            console.log(error);
        }
    }

    /**
     * Processes a batch of tickers for EPS calculation
     */
    async processBatchForEPS(batch: ListOfTickers[], batchIndex: number, totalBatches: number): Promise<void> {
        if (batch.length === 0) {
            return;
        }
        console.log(`[Batch ${batchIndex}/${totalBatches}] Processing ${batch.length} tickers for EPS`);
        const batchStartTime = Date.now();

        // Process all tickers in the batch sequentially (database operations)
        for (let i = 0; i < batch.length; i++) {
            const ticker = batch[i];

            try {
                console.log(`[Batch ${batchIndex}/${totalBatches}] Processing EPS for ticker: ${ticker.primary_ticker_eodhd} (ID: ${ticker.id})`);

                const income_statement_yearly = await IncomeStatement.findAll({
                    where: {
                        ticker_internal_id: ticker.id,
                        document_type_year_or_quarter: "y",
                    },
                    attributes: ["document_date", "net_income", "eps_diluted_current", "eps_diluted_last_date", "id", "ticker_internal_id"],
                    order: [["document_date", "ASC"]],
                });

                const document_date_yearly = [];

                for (let j = 0; j < income_statement_yearly.length; j++) {
                    document_date_yearly.push(income_statement_yearly[j].document_date);
                }

                const income_statement_quarterly = await IncomeStatement.findAll({
                    where: {
                        ticker_internal_id: ticker.id,
                        document_type_year_or_quarter: "q",
                    },
                    attributes: ["document_date", "net_income", "eps_diluted_current", "eps_diluted_last_date", "id", "ticker_internal_id"],
                    order: [["document_date", "ASC"]],
                });

                const document_date_quarterly = [];

                for (let j = 0; j < income_statement_quarterly.length; j++) {
                    document_date_quarterly.push(income_statement_quarterly[j].document_date);
                }

                const balancesheet_yearly = await BalanceSheet.findAll({
                    where: {
                        ticker_internal_id: ticker.id,
                        document_type_year_or_quarter: "y",
                        document_date: {
                            [Op.in]: document_date_yearly,
                        },
                    },
                    attributes: ["document_date", "common_stock_shares_outstanding", "id", "ticker_internal_id"],
                    order: [["document_date", "ASC"]],
                });

                const balancesheet_quarterly = await BalanceSheet.findAll({
                    where: {
                        ticker_internal_id: ticker.id,
                        document_type_year_or_quarter: "q",
                        document_date: {
                            [Op.in]: document_date_quarterly,
                        },
                    },
                    attributes: ["document_date", "common_stock_shares_outstanding", "id", "ticker_internal_id"],
                    order: [["document_date", "ASC"]],
                });

                await this.setEPSByDate(balancesheet_yearly, income_statement_yearly, Document_type_year_or_quarter.y);
                await this.setEPSByDate(balancesheet_quarterly, income_statement_quarterly, Document_type_year_or_quarter.q);
            } catch (err) {
                addLogJobExecution(
                    LogLevel.ERROR,
                    "setEPS",
                    "Error when try to set EPS for ticker",
                    {
                        error: err instanceof Error ? err.message : String(err),
                        ticker: ticker.primary_ticker_eodhd,
                    },
                    ticker.id,
                );
            }
        }

        const batchDuration = Date.now() - batchStartTime;
        console.log(`[Batch ${batchIndex}/${totalBatches}] Completed in ${batchDuration}ms`);

        addLogJobExecution(LogLevel.INFO, "setEPS", "Batch processing completed", {
            batchIndex,
            totalBatches,
            batchSize: batch.length,
            duration: batchDuration,
        });
    }

    /**
     * Processes batches sequentially
     */
    async processBatchesSequentiallyForEPS(batches: ListOfTickers[][], historyId?: string): Promise<void> {
        let processedTickers = 0;
        const historyObjectId = new ObjectId(historyId);
        for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
            const batch = batches[batchIndex];

            try {
                await this.processBatchForEPS(batch, batchIndex + 1, batches.length);
                processedTickers += batch.length;

                // Update progress tracking if historyId is provided
                if (historyId) {
                    try {
                        await updateJobProgress(historyObjectId, processedTickers);
                    } catch (error) {
                        console.error("Error updating job progress:", error);
                    }
                }
            } catch (err: any) {
                console.error(`[Batch ${batchIndex + 1}/${batches.length}] Error processing batch:`, err.message);
                addLogJobExecution(LogLevel.ERROR, "setEPS", "Batch processing error", {
                    batchIndex: batchIndex + 1,
                    totalBatches: batches.length,
                    error: err instanceof Error ? err.message : String(err),
                });

                // Continue processing remaining batches even if one fails
                processedTickers += batch.length;
            }
        }
    }

    /**
     * Processes batches concurrently with limited concurrency
     */
    async processBatchesConcurrentlyForEPS(batches: ListOfTickers[][], maxConcurrency: number, historyId?: string): Promise<void> {
        let processedTickers = 0;
        const historyObjectId = new ObjectId(historyId);
        // Process batches in chunks to limit concurrency
        for (let i = 0; i < batches.length; i += maxConcurrency) {
            const batchChunk = batches.slice(i, i + maxConcurrency);
            const chunkPromises = batchChunk.map(async (batch, chunkIndex) => {
                const batchIndex = i + chunkIndex;

                try {
                    await this.processBatchForEPS(batch, batchIndex + 1, batches.length);
                    return batch.length;
                } catch (err: any) {
                    console.error(`[Batch ${batchIndex + 1}/${batches.length}] Error processing concurrent batch:`, err.message);
                    addLogJobExecution(LogLevel.ERROR, "setEPS", "Concurrent batch processing error", {
                        batchIndex: batchIndex + 1,
                        totalBatches: batches.length,
                        error: err instanceof Error ? err.message : String(err),
                    });
                    return batch.length; // Still count as processed for progress tracking
                }
            });

            // Wait for this chunk to complete
            const chunkResults = await Promise.all(chunkPromises);
            const chunkProcessedCount = chunkResults.reduce((sum, count) => sum + count, 0);
            processedTickers += chunkProcessedCount;

            // Update progress tracking if historyId is provided
            if (historyId) {
                try {
                    await updateJobProgress(historyObjectId, processedTickers);
                } catch (error) {
                    console.error("Error updating job progress:", error);
                }
            }
        }
    }

    async setEPSByDate(balance_sheet: BalanceSheet[], income_statement: IncomeStatement[], document_type_year_or_quarter: Document_type_year_or_quarter) {
        if (income_statement.length === 0) {
            return;
        }

        // Step 1: Bulk fetch all CashFlow data needed for this ticker and document type
        const ticker_internal_id = income_statement[0].ticker_internal_id;
        const document_dates = income_statement.map((item) => item.document_date);

        const cash_flows = await CashFlow.findAll({
            where: {
                ticker_internal_id,
                document_type_year_or_quarter,
                document_date: {
                    [Op.in]: document_dates,
                },
            },
            attributes: ["document_date", "net_income", "ticker_internal_id"],
        });

        // Create a map for quick lookup of cash flow data by document_date
        const cash_flow_map = new Map<string, number>();
        cash_flows.forEach((cf) => {
            cash_flow_map.set(cf.document_date, cf.net_income || 0);
        });

        // Step 2: Process all income statements and prepare bulk updates
        const bulk_updates: Array<{
            id: number;
            eps_diluted_current: number;
            eps_diluted_last_date?: number;
        }> = [];

        for (let i = 0; i < income_statement.length; i++) {
            const document_date = income_statement[i].document_date;
            const balance_sheet_index = balance_sheet.findIndex((item) => item.document_date === document_date);

            if (balance_sheet_index > -1) {
                let {net_income} = income_statement[i];
                const {common_stock_shares_outstanding} = balance_sheet[balance_sheet_index];
                let eps_current = 0;

                // Use cached cash flow data instead of individual query
                if (isNull(net_income) || net_income === 0) {
                    net_income = cash_flow_map.get(document_date) || 0;
                }

                if (common_stock_shares_outstanding > 0) {
                    eps_current = net_income / common_stock_shares_outstanding;
                }

                const update_data: any = {
                    id: income_statement[i].id,
                    eps_diluted_current: eps_current,
                };

                if (i > 0) {
                    const document_last_date = income_statement[i - 1].document_date;
                    const balance_sheet_last_date_index = balance_sheet.findIndex((item) => item.document_date === document_last_date);

                    if (balance_sheet_last_date_index > -1) {
                        let net_income_last_date = income_statement[i - 1].net_income;
                        const common_stock_shares_outstanding_last_date = balance_sheet[balance_sheet_last_date_index].common_stock_shares_outstanding;

                        // Use cached cash flow data instead of individual query
                        if (isNull(net_income_last_date) || net_income_last_date === 0) {
                            net_income_last_date = cash_flow_map.get(document_last_date) || 0;
                        }

                        let eps_last_year = 0;
                        if (common_stock_shares_outstanding_last_date > 0) {
                            eps_last_year = net_income_last_date / common_stock_shares_outstanding_last_date;
                        }

                        update_data.eps_diluted_last_date = eps_last_year;
                    }
                }

                bulk_updates.push(update_data);
            }
        }

        // Step 3: Perform bulk update instead of individual saves
        if (bulk_updates.length > 0) {
            try {
                // Use Promise.all for concurrent updates to improve performance
                const update_promises = bulk_updates.map((update) =>
                    IncomeStatement.update(
                        {
                            eps_diluted_current: update.eps_diluted_current,
                            ...(update.eps_diluted_last_date !== undefined && {eps_diluted_last_date: update.eps_diluted_last_date}),
                        },
                        {
                            where: {id: update.id},
                        },
                    ),
                );

                await Promise.all(update_promises);

                addLogJobExecution(LogLevel.INFO, "setEPS", "Bulk EPS updates completed", {
                    ticker_internal_id,
                    document_type: document_type_year_or_quarter,
                    updates_count: bulk_updates.length,
                });
            } catch (error) {
                addLogJobExecution(LogLevel.ERROR, "setEPS", "Error in bulk EPS updates", {
                    ticker_internal_id,
                    document_type: document_type_year_or_quarter,
                    error: error instanceof Error ? error.message : String(error),
                });
                throw error;
            }
        }
    }
}

/**
 * Creates batches of tickers for batch processing
 */
function createBatchesForEPS(tickers: ListOfTickers[], batchSize: number): ListOfTickers[][] {
    const batches: ListOfTickers[][] = [];
    for (let i = 0; i < tickers.length; i += batchSize) {
        batches.push(tickers.slice(i, i + batchSize));
    }
    return batches;
}
