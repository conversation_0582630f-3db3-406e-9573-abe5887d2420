import {ListOfTickers} from "@/entities/consolidated_data/ListOfTickers";
import {ApiEOD} from "../providers/implements/ApiEOD";
import {HistoricalSplitsRepository} from "../repositories/implements/HistoricalSplitsRepository";
import {addLogJobExecution} from "@/lib/agenda";
import {LogLevel} from "@/utils/types/logs/log";
import {iSplitsDAO} from "@/repositories/iSplitsDAO";

export class SplitController {
    async fillSplits(updatedTickers: ListOfTickers[], historyId?: string) {
        try {
            addLogJobExecution(LogLevel.INFO, "fillSplits", "Retrieving splits for tickers", {tickersLength: updatedTickers.length});

            if (updatedTickers.length === 0) {
                return;
            }

            // Get configurable batch size
            const {getSplitsBatchSize} = await import("../lib/agenda");
            const batchSize = await getSplitsBatchSize();
            console.log(`Processing ${updatedTickers.length} tickers with batch size: ${batchSize}`);

            addLogJobExecution(LogLevel.INFO, "fillSplits", "Starting batch splits processing", {
                totalTickers: updatedTickers.length,
                batchSize: batchSize,
            });

            // Initialize progress tracking if historyId is provided
            if (historyId) {
                const {initializeJobProgress} = await import("../lib/jobDefinitions");
                const {ObjectId} = await import("mongodb");
                try {
                    await initializeJobProgress(new ObjectId(historyId), updatedTickers.length);
                } catch (error) {
                    console.error("Error initializing job progress:", error);
                }
            }

            // Process tickers in batches
            const batches = createBatchesForSplits(updatedTickers, batchSize);

            // Determine if we should process batches concurrently or sequentially
            const maxConcurrentBatches = Math.min(3, batches.length); // Limit concurrent batches to avoid overwhelming the API
            const shouldProcessConcurrently = batches.length > 1 && maxConcurrentBatches > 1;

            if (shouldProcessConcurrently) {
                console.log(`Processing ${batches.length} batches with max ${maxConcurrentBatches} concurrent batches`);
                await processBatchesConcurrentlyForSplits(batches, maxConcurrentBatches, historyId);
            } else {
                console.log(`Processing ${batches.length} batches sequentially`);
                await processBatchesSequentiallyForSplits(batches, historyId);
            }

            addLogJobExecution(LogLevel.INFO, "fillSplits", "Splits processing completed", {
                totalTickers: updatedTickers.length,
                batchesProcessed: batches.length,
            });
        } catch (error) {
            addLogJobExecution(LogLevel.ERROR, "fillSplits", "Error when try to get splits", {error: error instanceof Error ? error.message : String(error)});
            console.log(error);
        }
    }
}

/**
 * Creates batches of tickers for batch processing
 */
function createBatchesForSplits(tickers: ListOfTickers[], batchSize: number): ListOfTickers[][] {
    const batches: ListOfTickers[][] = [];
    for (let i = 0; i < tickers.length; i += batchSize) {
        batches.push(tickers.slice(i, i + batchSize));
    }
    return batches;
}

/**
 * Processes a batch of tickers for splits
 */
async function processBatchForSplits(batch: ListOfTickers[], batchIndex: number, totalBatches: number): Promise<void> {
    if (batch.length === 0) {
        return;
    }

    console.log(`[Batch ${batchIndex}/${totalBatches}] Processing ${batch.length} tickers for splits`);
    const batchStartTime = Date.now();

    // Process all tickers in the batch concurrently
    const promises = batch.map(async (ticker) => {
        try {
            console.log(`[Batch ${batchIndex}/${totalBatches}] Processing ticker: ${ticker.primary_ticker_eodhd} (ID: ${ticker.id})`);
            const api = new ApiEOD();
            const response: iSplitsDAO[] = await api.findSplits(ticker.primary_ticker_eodhd);

            if (response?.length > 0) {
                const ticker_repository = new HistoricalSplitsRepository(ticker.id || 0);
                await ticker_repository.saveSplitsData(response);
            }
        } catch (err) {
            addLogJobExecution(
                LogLevel.ERROR,
                "fillSplits",
                "Error when try to get splits for ticker",
                {
                    error: err instanceof Error ? err.message : String(err),
                    ticker: ticker.primary_ticker_eodhd,
                },
                ticker.id,
            );
        }
    });

    await Promise.all(promises);

    const batchDuration = Date.now() - batchStartTime;
    console.log(`[Batch ${batchIndex}/${totalBatches}] Completed in ${batchDuration}ms`);

    addLogJobExecution(LogLevel.INFO, "fillSplits", "Batch processing completed", {
        batchIndex,
        totalBatches,
        batchSize: batch.length,
        duration: batchDuration,
    });
}

/**
 * Processes batches sequentially
 */
async function processBatchesSequentiallyForSplits(batches: ListOfTickers[][], historyId?: string): Promise<void> {
    let processedTickers = 0;

    for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
        const batch = batches[batchIndex];

        try {
            await processBatchForSplits(batch, batchIndex + 1, batches.length);
            processedTickers += batch.length;

            // Update progress tracking if historyId is provided
            if (historyId) {
                const {updateJobProgress} = await import("../lib/jobDefinitions");
                const {ObjectId} = await import("mongodb");
                try {
                    await updateJobProgress(new ObjectId(historyId), processedTickers);
                } catch (error) {
                    console.error("Error updating job progress:", error);
                }
            }
        } catch (err: any) {
            console.error(`[Batch ${batchIndex + 1}/${batches.length}] Error processing batch:`, err.message);
            addLogJobExecution(LogLevel.ERROR, "fillSplits", "Batch processing error", {
                batchIndex: batchIndex + 1,
                totalBatches: batches.length,
                error: err instanceof Error ? err.message : String(err),
            });

            // Continue processing remaining batches even if one fails
            processedTickers += batch.length;
        }
    }
}

/**
 * Processes batches concurrently with limited concurrency
 */
async function processBatchesConcurrentlyForSplits(batches: ListOfTickers[][], maxConcurrency: number, historyId?: string): Promise<void> {
    let processedTickers = 0;

    // Process batches in chunks to limit concurrency
    for (let i = 0; i < batches.length; i += maxConcurrency) {
        const batchChunk = batches.slice(i, i + maxConcurrency);
        const chunkPromises = batchChunk.map(async (batch, chunkIndex) => {
            const batchIndex = i + chunkIndex;

            try {
                await processBatchForSplits(batch, batchIndex + 1, batches.length);
                return batch.length;
            } catch (err: any) {
                console.error(`[Batch ${batchIndex + 1}/${batches.length}] Error processing concurrent batch:`, err.message);
                addLogJobExecution(LogLevel.ERROR, "fillSplits", "Concurrent batch processing error", {
                    batchIndex: batchIndex + 1,
                    totalBatches: batches.length,
                    error: err instanceof Error ? err.message : String(err),
                });
                return batch.length; // Still count as processed for progress tracking
            }
        });

        // Wait for this chunk to complete
        const chunkResults = await Promise.all(chunkPromises);
        const chunkProcessedCount = chunkResults.reduce((sum, count) => sum + count, 0);
        processedTickers += chunkProcessedCount;

        // Update progress tracking if historyId is provided
        if (historyId) {
            const {updateJobProgress} = await import("../lib/jobDefinitions");
            const {ObjectId} = await import("mongodb");
            try {
                await updateJobProgress(new ObjectId(historyId), processedTickers);
            } catch (error) {
                console.error("Error updating job progress:", error);
            }
        }
    }
}
