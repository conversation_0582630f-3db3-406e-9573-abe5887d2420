import {EndpointType, LatestApi} from "@/entities/consolidated_data/LatestApi";
import {StatisticsOfAPIRepository} from "@/repositories/implements/StatisticsOfAPIRepository";

export class StatisticsOfAPIController {
    async createStatisticsOfAPI(request_date: Date, latest_api: LatestApi = LatestApi.eodhd, api_credit_consumption: number, request_url?: string, endpoint_type?: EndpointType) {
        try {
            // Validate required fields before creating
            if (!request_date || !latest_api || typeof api_credit_consumption !== "number") {
                console.warn("Skipping StatisticsOfAPI creation: missing required fields", {
                    request_date,
                    latest_api,
                    api_credit_consumption,
                    request_url,
                });
                return;
            }

            const statisticsAPI = {
                request_date,
                latest_api,
                api_credit_consumption,
                request_url: request_url || undefined, // Ensure it's undefined if empty
                endpoint_type: endpoint_type || undefined, // Ensure it's undefined if empty
            };

            const statisticsOfAPIRepository = new StatisticsOfAPIRepository();
            await statisticsOfAPIRepository.create(statisticsAPI);
        } catch (error: any) {
            console.error("Error creating StatisticsOfAPI:", error.message);
        }
    }

    async getStatisticsOfAPI(request_date: Date, latest_api: LatestApi = LatestApi.eodhd) {
        try {
            const statisticsOfAPIRepository = new StatisticsOfAPIRepository();
            return await statisticsOfAPIRepository.getStatisticsOfAPI(request_date, latest_api);
        } catch (error: any) {
            console.log(error.message);
        }
    }

    async getStatisticsOfAPICurrentComsumption(latest_api: LatestApi = LatestApi.eodhd) {
        try {
            const statisticsOfAPIRepository = new StatisticsOfAPIRepository();
            return await statisticsOfAPIRepository.getStatisticsOfAPICurrentComsumption(latest_api);
        } catch (error: any) {
            console.log(error.message);
        }
    }

    async getApiCreditsForPastDays(days: number, latest_api: LatestApi = LatestApi.eodhd) {
        try {
            const statisticsOfAPIRepository = new StatisticsOfAPIRepository();
            return await statisticsOfAPIRepository.getApiCreditsForPastDays(days, latest_api);
        } catch (error: any) {
            console.log(error.message);
            return [];
        }
    }
}
