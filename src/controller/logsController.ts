import {Latest<PERSON>pi} from "@/entities/consolidated_data/LatestApi";
import {Logs} from "@/entities/consolidated_data/Logs";
import {LogsRepository} from "@/repositories/implements/LogsRepository";

export class LogsController {
    async tickerUpdatedEODHD(ticker_internal_id: number, latest_log: string) {
        await this.createOrUpdateLog({
            ticker_internal_id,
            latest_api: LatestApi.eodhd,
            latest_log: latest_log,
        });
    }

    async dynamoDBQueued(
        ticker_internal_id: number,
        latest_log: string,
        quantity_of_balance_sheet_year: number,
        quantity_of_balance_sheet_quarter: number,
        quantity_of_cash_flow_year: number,
        quantity_of_cash_flow_quarter: number,
        quantity_of_income_statement_year: number,
        quantity_of_income_statement_quarter: number,
        start_of_balance_sheet_year: Date | null,
        end_of_balance_sheet_year: Date | null,
        start_of_cash_flow_year: Date | null,
        end_of_cash_flow_year: Date | null,
        start_of_income_statement_year: Date | null,
        end_of_income_statement_year: Date | null,
        start_of_balance_sheet_quarter: Date | null,
        end_of_balance_sheet_quarter: Date | null,
        start_of_cash_flow_quarter: Date | null,
        end_of_cash_flow_quarter: Date | null,
        start_of_income_statement_quarter: Date | null,
        end_of_income_statement_quarter: Date | null,
    ) {
        await this.createOrUpdateLog({
            ticker_internal_id,
            latest_api: LatestApi.eodhd,
            latest_log,
            quantity_of_balance_sheet_year,
            quantity_of_balance_sheet_quarter,
            quantity_of_cash_flow_year,
            quantity_of_cash_flow_quarter,
            quantity_of_income_statement_year,
            quantity_of_income_statement_quarter,
            start_of_balance_sheet_year,
            end_of_balance_sheet_year,
            start_of_cash_flow_year,
            end_of_cash_flow_year,
            start_of_income_statement_year,
            end_of_income_statement_year,
            start_of_balance_sheet_quarter,
            end_of_balance_sheet_quarter,
            start_of_cash_flow_quarter,
            end_of_cash_flow_quarter,
            start_of_income_statement_quarter,
            end_of_income_statement_quarter,
        });
    }

    static async saveError(ticker_internal_id: number, latest_log: string) {
        const logsRepository = new LogsRepository();
        await logsRepository.createOrUpdateLog({
            ticker_internal_id,
            latest_api: LatestApi.eodhd,
            latest_log,
        });
    }

    async createOrUpdateLog(log: Partial<Logs>) {
        const logsRepository = new LogsRepository();
        await logsRepository.createOrUpdateLog(log);
    }
}
