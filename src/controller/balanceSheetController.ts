import {FilesToBalanceExecute} from "../execute/filesTobalanceExecute";
import {LogsOfTickersExecute} from "../execute/logsOfTickersExecute";
import {BalanceSheetRepository} from "../repositories/implements/BalanceSheetRepository";
import {LogsController} from "./logsController";

export class BalanceSheetController {
    async parseData(ticker_internal_id: number, file: any) {
        try {
            // balancesheet

            const balanceSheetRepository = new BalanceSheetRepository(ticker_internal_id);

            const filesToBalanceSheet = new FilesToBalanceExecute(balanceSheetRepository, file);

            return await filesToBalanceSheet.execute();
        } catch (error: any) {
            console.log("Balance sheet", error.message);

            await LogsController.saveError(ticker_internal_id, error.message);
        }
    }
}
