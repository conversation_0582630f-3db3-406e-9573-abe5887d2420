import {addLogJobExecution} from "../lib/agenda";
import {LogLevel} from "@/utils/types/logs/log";
import {ListOfTickers} from "../entities/consolidated_data/ListOfTickers";
import {getFundamentalDataAPI_EOD} from "./tickersController";
import {FundamentalDataRepository} from "../repositories/implements/FundamentaDataRepository";
import {LogsController} from "./logsController";
import {StatisticsOfTicker} from "../entities/consolidated_data/StatisticsOfTickers";
import {ReasonNotEnable} from "../utils/types/EODHD/exchange_countries";
import axios from "axios";

export async function getFundamentalDataController(updatedTickers: ListOfTickers[], historyId?: string): Promise<void> {
    try {
        addLogJobExecution(LogLevel.INFO, "getFundamentalDataController", "Retrieving fundamental data for tickers", {tickersLength: updatedTickers.length});

        if (updatedTickers.length === 0) {
            return;
        }

        // Get configurable batch size
        const {getFundamentalDataBatchSize} = await import("../lib/agenda");
        const batchSize = await getFundamentalDataBatchSize();
        console.log(`Processing ${updatedTickers.length} tickers with batch size: ${batchSize}`);

        addLogJobExecution(LogLevel.INFO, "getFundamentalDataController", "Starting batch fundamental data processing", {
            totalTickers: updatedTickers.length,
            batchSize: batchSize,
        });

        // Initialize progress tracking if historyId is provided
        if (historyId) {
            const {initializeJobProgress} = await import("../lib/jobDefinitions");
            const {ObjectId} = await import("mongodb");
            try {
                await initializeJobProgress(new ObjectId(historyId), updatedTickers.length);
            } catch (error) {
                console.error("Error initializing job progress:", error);
            }
        }

        // Process tickers in batches
        const batches = createBatches(updatedTickers, batchSize);

        // Determine if we should process batches concurrently or sequentially
        const maxConcurrentBatches = Math.min(3, batches.length); // Limit concurrent batches to avoid overwhelming the API
        const shouldProcessConcurrently = batches.length > 1 && maxConcurrentBatches > 1;

        if (shouldProcessConcurrently) {
            console.log(`Processing ${batches.length} batches with max ${maxConcurrentBatches} concurrent batches`);
            await processBatchesConcurrently(batches, maxConcurrentBatches, historyId);
        } else {
            console.log(`Processing ${batches.length} batches sequentially`);
            await processBatchesSequentially(batches, historyId);
        }

        addLogJobExecution(LogLevel.INFO, "getFundamentalDataController", "Fundamental data processing completed", {
            totalTickers: updatedTickers.length,
            batchesProcessed: batches.length,
        });
    } catch (error) {
        addLogJobExecution(LogLevel.ERROR, "getFundamentalDataController", "Error when try to get fundamental data", {error: error instanceof Error ? error.message : String(error)});
        console.log(error);
    }
}

/**
 * Creates batches of tickers for batch processing
 */
function createBatches(tickers: ListOfTickers[], batchSize: number): ListOfTickers[][] {
    const batches: ListOfTickers[][] = [];
    for (let i = 0; i < tickers.length; i += batchSize) {
        batches.push(tickers.slice(i, i + batchSize));
    }
    return batches;
}

/**
 * Processes a batch of tickers for fundamental data
 */
async function processBatch(batch: ListOfTickers[], batchIndex: number, totalBatches: number): Promise<void> {
    if (batch.length === 0) {
        return;
    }

    console.log(`[Batch ${batchIndex}/${totalBatches}] Processing ${batch.length} tickers for fundamental data`);
    const batchStartTime = Date.now();

    // Process all tickers in the batch concurrently
    const promises = batch.map(async (ticker) => {
        // Skip if ticker or id is undefined
        if (!ticker || ticker.id === undefined) {
            console.log(`[Batch ${batchIndex}/${totalBatches}] Skipping ticker: Missing ticker or undefined id`);
            return;
        }

        try {
            console.log(`[Batch ${batchIndex}/${totalBatches}] Processing ticker: ${ticker.primary_ticker_eodhd} (ID: ${ticker.id})`);
            const logsController = new LogsController();

            console.log(`Fetching fundamental data for ${ticker.primary_ticker_eodhd}`);
            const data = await getFundamentalDataAPI_EOD(ticker.primary_ticker_eodhd);

            const fundamentalData = new FundamentalDataRepository();
            console.log(`Saving fundamental data for ${ticker.primary_ticker_eodhd}`);

            const fileName = await fundamentalData.create(
                JSON.stringify({
                    ticker_internal_id: ticker.id,
                    ...data,
                }),
                ticker.primary_ticker_eodhd,
            );

            ticker.fundamental_data_last_updated = new Date();
            ticker.updated_at = new Date();
            await ticker.save();

            console.log(`Successfully processed ${ticker.primary_ticker_eodhd}`);
            logsController.tickerUpdatedEODHD(ticker.id, `Fundamental data updated with success and saved on S3 for ${ticker.primary_ticker_eodhd} with name ${fileName}`);

            addLogJobExecution(LogLevel.INFO, "getFundamentalDataController", `Successfully processed ${ticker.primary_ticker_eodhd}`, {fileName}, ticker.id);
        } catch (error: any) {
            console.log(`Error processing ${ticker.primary_ticker_eodhd}: ${error.message}`);
            const logsController = new LogsController();

            if (error.message.includes("Symbol not found")) {
                ticker.is_enable = 0;
                ticker.log_eodhd = "Symbol not found";
                await ticker.update({is_enable: 0, log_eodhd: error.message, reason_not_enable: ReasonNotEnable.SYMBOL_NOT_FOUND});
                await ticker.save();
                await StatisticsOfTicker.destroy({
                    where: {
                        ticker_internal_id: ticker.id,
                    },
                });
                console.log(`Ticker ${ticker.primary_ticker_eodhd} disabled: Symbol not found`);
                addLogJobExecution(LogLevel.ERROR, "getFundamentalDataController", `Ticker ${ticker.primary_ticker_eodhd} disabled: Symbol not found. Statistics deleted`, {error}, ticker.id);
            } else if (axios.isAxiosError(error)) {
                // Handle network or API-related errors
                const statusCode = error.response?.status;
                const errorMessage = error.response?.data?.message || error.message;
                ticker.log_eodhd = `API Error (${statusCode}): ${errorMessage}`;
                // Only disable ticker for certain error types (e.g., 404)
                if (statusCode === 404) {
                    ticker.is_enable = 0;
                    await ticker.update({is_enable: 0, log_eodhd: errorMessage, reason_not_enable: ReasonNotEnable.API_404_RESPONSE});
                    await ticker.save();
                    await StatisticsOfTicker.destroy({
                        where: {
                            ticker_internal_id: ticker.id,
                        },
                    });
                    console.log(`Ticker ${ticker.primary_ticker_eodhd} disabled: API returned 404`);
                    addLogJobExecution(LogLevel.ERROR, "getFundamentalDataController", `Ticker ${ticker.primary_ticker_eodhd} disabled: API returned 404. Statistics deleted`, {error}, ticker.id);
                } else {
                    console.log(`API error for ${ticker.primary_ticker_eodhd}: ${statusCode} - ${errorMessage}`);
                }
            } else {
                // Handle other types of errors
                ticker.log_eodhd = `Error: ${error.message}`;
                console.log(`Unexpected error for ${ticker.primary_ticker_eodhd}: ${error.message}`);
            }
            ticker.updated_at = new Date();
            await ticker.save();
            logsController.tickerUpdatedEODHD(ticker.id, ticker.log_eodhd);
            addLogJobExecution(LogLevel.ERROR, "getFundamentalDataController", `Error processing ${ticker.primary_ticker_eodhd}: ${error.message}`, {}, ticker.id);
        }
    });

    await Promise.all(promises);

    const batchDuration = Date.now() - batchStartTime;
    console.log(`[Batch ${batchIndex}/${totalBatches}] Completed in ${batchDuration}ms`);

    addLogJobExecution(LogLevel.INFO, "getFundamentalDataController", "Batch processed successfully", {
        batchIndex: batchIndex,
        totalBatches: totalBatches,
        batchSize: batch.length,
        durationMs: batchDuration,
    });
}

/**
 * Processes batches sequentially
 */
async function processBatchesSequentially(batches: ListOfTickers[][], historyId?: string): Promise<void> {
    let processedTickers = 0;

    for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
        const batch = batches[batchIndex];

        try {
            await processBatch(batch, batchIndex + 1, batches.length);
            processedTickers += batch.length;

            // Update progress tracking if historyId is provided
            if (historyId) {
                const {updateJobProgress} = await import("../lib/jobDefinitions");
                const {ObjectId} = await import("mongodb");
                try {
                    await updateJobProgress(new ObjectId(historyId), processedTickers);
                } catch (error) {
                    console.error("Error updating job progress:", error);
                }
            }
        } catch (err: any) {
            console.error(`[Batch ${batchIndex + 1}/${batches.length}] Error processing batch:`, err.message);
            addLogJobExecution(LogLevel.ERROR, "getFundamentalDataController", "Batch processing error", {
                batchIndex: batchIndex + 1,
                totalBatches: batches.length,
                error: err instanceof Error ? err.message : String(err),
            });

            // Continue processing remaining batches even if one fails
            processedTickers += batch.length;
        }
    }
}

/**
 * Processes batches concurrently with limited concurrency
 */
async function processBatchesConcurrently(batches: ListOfTickers[][], maxConcurrency: number, historyId?: string): Promise<void> {
    let processedTickers = 0;

    // Process batches in chunks to limit concurrency
    for (let i = 0; i < batches.length; i += maxConcurrency) {
        const batchChunk = batches.slice(i, i + maxConcurrency);
        const chunkPromises = batchChunk.map(async (batch, chunkIndex) => {
            const batchIndex = i + chunkIndex;

            try {
                await processBatch(batch, batchIndex + 1, batches.length);
                return batch.length;
            } catch (err: any) {
                console.error(`[Batch ${batchIndex + 1}/${batches.length}] Error processing concurrent batch:`, err.message);
                addLogJobExecution(LogLevel.ERROR, "getFundamentalDataController", "Concurrent batch processing error", {
                    batchIndex: batchIndex + 1,
                    totalBatches: batches.length,
                    error: err instanceof Error ? err.message : String(err),
                });
                return batch.length; // Still count as processed for progress tracking
            }
        });

        // Wait for this chunk to complete
        const chunkResults = await Promise.all(chunkPromises);
        const chunkProcessedCount = chunkResults.reduce((sum, count) => sum + count, 0);
        processedTickers += chunkProcessedCount;

        // Update progress tracking if historyId is provided
        if (historyId) {
            const {updateJobProgress} = await import("../lib/jobDefinitions");
            const {ObjectId} = await import("mongodb");
            try {
                await updateJobProgress(new ObjectId(historyId), processedTickers);
            } catch (error) {
                console.error("Error updating job progress:", error);
            }
        }
    }
}
