import {addLogJobExecution} from "@/lib/agenda";
import {iFundamentalData} from "../entities/EOD/iFundamentalData";
import {ListOfTickers} from "../entities/consolidated_data/ListOfTickers";
import {StatisticsOfTicker} from "../entities/consolidated_data/StatisticsOfTickers";
import {LogsOfTickersExecute} from "../execute/logsOfTickersExecute";
import {FundamentalDataRepository} from "../repositories/implements/FundamentaDataRepository";
import {FundamentalDataType} from "../utils/types/EODHD/FundamentalDataType";
import {Op} from "sequelize";
import {LogLevel} from "@/utils/types/logs/log";
import {BalanceSheetController} from "./balanceSheetController";
import {IncomeStatementController} from "./incomeStatementController";
import {CashFlowController} from "./cashFlowController";
import {LogsController} from "./logsController";
import {ListOfTickersRepository} from "@/repositories/implements/ListOfTickersRepository";
import {Sector} from "@/entities/consolidated_data/Sectors";
import {StatisticsOfTickerRepository} from "@/repositories/implements/StatisticsOfTickerRepository";

/**
 * Get tickers that had their fundamental_data_last_updated changed today
 * @returns Array of tickers updated today
 */
export async function getTickersUpdatedLastXDays(lastXDays: number = 8): Promise<ListOfTickers[]> {
    console.log("Retrieving tickers updated:" + lastXDays + " days ago today");
    addLogJobExecution(LogLevel.INFO, "getTickersUpdatedLastXDays", "Retrieving tickers updated:" + lastXDays + " days ago today", {lastXDays});
    const startTime = Date.now();

    // Create start of dateStart date
    const dateStart = new Date();
    dateStart.setDate(dateStart.getDate() - lastXDays);
    dateStart.setHours(0, 0, 0, 0);

    console.log(`Date (start): ${dateStart.toISOString()}`);

    const params: any = {
        attributes: ["primary_ticker_eodhd", "id"],
        order: [["id", "ASC"]],
        where: {
            is_enable: 1,
            fundamental_data_last_updated: {
                [Op.gte]: dateStart,
            },
        },
    };

    const updatedTickers = await ListOfTickers.findAll(params);
    const executionTime = Date.now() - startTime;

    console.log(`Found ${updatedTickers.length} tickers updated in past ${lastXDays} days in ${executionTime}ms`);
    addLogJobExecution(LogLevel.INFO, "getTickersUpdatedLastXDays", "`Found ${updatedTickers.length} tickers updated in past ${lastXDays} days in ${executionTime}ms`", {
        updatedTickersLength: updatedTickers.length,
    });
    return updatedTickers;
}

export async function processS3FilesFinalcials(updatedTickers: ListOfTickers[], historyId?: string): Promise<any> {
    addLogJobExecution(LogLevel.INFO, "processS3FilesFinalcials", "Processing S3 files", {});

    const fundamentalData = new FundamentalDataRepository();

    // Initialize progress tracking if historyId is provided
    if (historyId) {
        const {initializeJobProgress} = await import("../lib/jobDefinitions");
        const {ObjectId} = await import("mongodb");
        try {
            await initializeJobProgress(new ObjectId(historyId), updatedTickers.length);
        } catch (error) {
            console.error("Error initializing job progress:", error);
        }
    }

    for (let i = 0; i < updatedTickers.length; i++) {
        const ticker = updatedTickers[i];
        const key = `fundamentals_${ticker.primary_ticker_eodhd}_eod.json`;
        try {
            console.log(`Processing ticker ${i + 1}/${updatedTickers.length}: ${ticker.primary_ticker_eodhd} (ID: ${ticker.id})`);
            addLogJobExecution(LogLevel.INFO, "processS3Files", `Processing ticker ${i + 1}/${updatedTickers.length}: ${ticker.primary_ticker_eodhd} (ID: ${ticker.id})`, {}, ticker.id);
            const file: FundamentalDataType = await fundamentalData.get(key);
            if (!file || Object.keys(file).length === 0) {
                console.log(`No file found for ${ticker.primary_ticker_eodhd}`);
                addLogJobExecution(LogLevel.WARNING, "processS3Files", `No file found for ${ticker.primary_ticker_eodhd}`, {}, ticker.id);
                continue;
            }
            await saveFundamentalData(file);
            addLogJobExecution(LogLevel.INFO, "processS3Files", "Fundamental data, Balance Sheet, Income Statement and Cash Flow QUEUED successfully", {}, ticker.id);
        } catch (error: any) {
            console.log(`Error processing ticker ${ticker.primary_ticker_eodhd}: ${error.message}`);
            addLogJobExecution(LogLevel.ERROR, "processS3Files", `Error processing ticker ${ticker.primary_ticker_eodhd}: ${error.message}`, {}, ticker.id);
        }

        // Update progress tracking if historyId is provided
        if (historyId) {
            const {updateJobProgress} = await import("../lib/jobDefinitions");
            const {ObjectId} = await import("mongodb");
            try {
                await updateJobProgress(new ObjectId(historyId), i + 1);
            } catch (error) {
                console.error("Error updating job progress:", error);
            }
        }
    }
}

export async function saveFundamentalData(data: FundamentalDataType) {
    const {ticker_internal_id, Financials} = data;
    const id = parseInt(ticker_internal_id || "0");
    try {
        const balance_sheet = Financials?.Balance_Sheet || {};
        const income_statement = Financials?.Income_Statement || {};
        const cash_flow = Financials?.Cash_Flow || {};

        const balanceSheet = new BalanceSheetController();
        const balanceSheetData = await balanceSheet.parseData(id, balance_sheet);
        console.log("balancesheet was QUEUED");

        const incomeStatement = new IncomeStatementController();
        const incomeStatementData = await incomeStatement.parseData(id, income_statement);
        console.log("income statement was QUEUED");

        const cashFlow = new CashFlowController();
        const cashFlowData = await cashFlow.parseData(id, cash_flow);
        console.log("cash flow was QUEUED");

        const logsController = new LogsController();

        if (!balanceSheetData || !incomeStatementData || !cashFlowData) {
            logsController.tickerUpdatedEODHD(id, "Missing data");
            throw new Error("Missing data");
        }

        logsController.dynamoDBQueued(
            id,
            "Fundamental data, Balance Sheet, Income Statement and Cash Flow QUEUED successfully",
            balanceSheetData.quantity_of_balance_sheet_year,
            balanceSheetData.quantity_of_balance_sheet_quarter,
            cashFlowData.quantity_of_cash_flow_year,
            cashFlowData.quantity_of_cash_flow_quarter,
            incomeStatementData.quantity_of_income_statement_year,
            incomeStatementData.quantity_of_income_statement_quarter,
            balanceSheetData.start_of_balance_sheet_year,
            balanceSheetData.end_of_balance_sheet_year,
            cashFlowData.start_of_cash_flow_year,
            cashFlowData.end_of_cash_flow_year,
            incomeStatementData.start_of_income_statement_year,
            incomeStatementData.end_of_income_statement_year,
            balanceSheetData.start_of_balance_sheet_quarter,
            balanceSheetData.end_of_balance_sheet_quarter,
            cashFlowData.start_of_cash_flow_quarter,
            cashFlowData.end_of_cash_flow_quarter,
            incomeStatementData.start_of_income_statement_quarter,
            incomeStatementData.end_of_income_statement_quarter,
        );
        addLogJobExecution(LogLevel.INFO, "saveFundamentalData", "Fundamental data, Balance Sheet, Income Statement and Cash Flow QUEUED successfully", {}, id);
    } catch (error: any) {
        addLogJobExecution(LogLevel.ERROR, "saveFundamentalData", "Error when try to save fundamental data", {error: error instanceof Error ? error.message : String(error)}, id);
        console.log("Error saveFundamentalData", error);
    }
}

export async function processS3FilesGeneralData(updatedTickers: ListOfTickers[], historyId?: string): Promise<any> {
    try {
        addLogJobExecution(LogLevel.INFO, "processS3FilesGeneralData", "Processing S3 files General Data", {tickersLength: updatedTickers.length});

        if (updatedTickers.length === 0) {
            return;
        }

        // Get configurable batch size
        const {getProcessS3FilesBatchSize} = await import("../lib/agenda");
        const batchSize = await getProcessS3FilesBatchSize();
        console.log(`Processing ${updatedTickers.length} tickers with batch size: ${batchSize}`);

        addLogJobExecution(LogLevel.INFO, "processS3FilesGeneralData", "Starting batch S3 files general data processing", {
            totalTickers: updatedTickers.length,
            batchSize: batchSize,
        });

        // Initialize progress tracking if historyId is provided
        if (historyId) {
            const {initializeJobProgress} = await import("../lib/jobDefinitions");
            const {ObjectId} = await import("mongodb");
            try {
                await initializeJobProgress(new ObjectId(historyId), updatedTickers.length);
            } catch (error) {
                console.error("Error initializing job progress:", error);
            }
        }

        // Process tickers in batches
        const batches = createBatchesForS3Files(updatedTickers, batchSize);

        // Determine if we should process batches concurrently or sequentially
        const maxConcurrentBatches = Math.min(3, batches.length); // Limit concurrent batches to avoid overwhelming S3
        const shouldProcessConcurrently = batches.length > 1 && maxConcurrentBatches > 1;

        if (shouldProcessConcurrently) {
            console.log(`Processing ${batches.length} batches with max ${maxConcurrentBatches} concurrent batches`);
            await processBatchesConcurrentlyForS3Files(batches, maxConcurrentBatches, historyId);
        } else {
            console.log(`Processing ${batches.length} batches sequentially`);
            await processBatchesSequentiallyForS3Files(batches, historyId);
        }

        addLogJobExecution(LogLevel.INFO, "processS3FilesGeneralData", "S3 files general data processing completed", {
            totalTickers: updatedTickers.length,
            batchesProcessed: batches.length,
        });
    } catch (error) {
        addLogJobExecution(LogLevel.ERROR, "processS3FilesGeneralData", "Error when try to process S3 files general data", {error: error instanceof Error ? error.message : String(error)});
        console.log(error);
    }
}

export async function saveGeneralData(data: FundamentalDataType, sectors: Sector[], listOfTickersRepository: ListOfTickersRepository) {
    const {ticker_internal_id, General, Highlights} = data;
    const id = parseInt(ticker_internal_id || "0");

    try {
        const dividend = Highlights?.DividendYield || 0;
        const statisticsOfTickerRepository = new StatisticsOfTickerRepository();
        await statisticsOfTickerRepository.updateDividendYield(id, dividend * 100);

        const sector = General?.Sector || "";
        listOfTickersRepository.updateSector(id, sector, sectors);

        addLogJobExecution(LogLevel.INFO, "saveGeneralData", "General data QUEUED successfully", {}, id);
    } catch (error: any) {
        addLogJobExecution(LogLevel.ERROR, "saveGeneralData", "Error when try to save general data", {error: error instanceof Error ? error.message : String(error)}, id);
        console.log("Error saveFundamentalData", error);
    }
}

/**
 * Creates batches of tickers for S3 files processing
 */
function createBatchesForS3Files(tickers: ListOfTickers[], batchSize: number): ListOfTickers[][] {
    const batches: ListOfTickers[][] = [];
    for (let i = 0; i < tickers.length; i += batchSize) {
        batches.push(tickers.slice(i, i + batchSize));
    }
    return batches;
}

/**
 * Processes a single batch of tickers for S3 files general data
 */
async function processBatchForS3Files(batch: ListOfTickers[], batchIndex: number, totalBatches: number): Promise<void> {
    const fundamentalData = new FundamentalDataRepository();
    const listOfTickersRepository = new ListOfTickersRepository();
    const sectors = await listOfTickersRepository.getSectors();

    console.log(`[Batch ${batchIndex}/${totalBatches}] Processing ${batch.length} tickers`);
    addLogJobExecution(LogLevel.INFO, "processBatchForS3Files", `Processing batch ${batchIndex}/${totalBatches} with ${batch.length} tickers`, {
        batchIndex,
        totalBatches,
        batchSize: batch.length,
    });

    // Process all tickers in the batch concurrently
    const promises = batch.map(async (ticker) => {
        const key = `fundamentals_${ticker.primary_ticker_eodhd}_eod.json`;
        try {
            console.log(`[Batch ${batchIndex}/${totalBatches}] Processing ticker: ${ticker.primary_ticker_eodhd} (ID: ${ticker.id})`);
            const file: FundamentalDataType = await fundamentalData.get(key);

            if (!file || Object.keys(file).length === 0) {
                console.log(`[Batch ${batchIndex}/${totalBatches}] No file found for ${ticker.primary_ticker_eodhd}`);
                addLogJobExecution(LogLevel.WARNING, "processBatchForS3Files", `No file found for ${ticker.primary_ticker_eodhd}`, {}, ticker.id);
                return;
            }

            await saveGeneralData(file, sectors, listOfTickersRepository);
            addLogJobExecution(LogLevel.INFO, "processBatchForS3Files", "General data QUEUED successfully", {}, ticker.id);
        } catch (error: any) {
            console.log(`[Batch ${batchIndex}/${totalBatches}] Error processing ticker ${ticker.primary_ticker_eodhd}: ${error.message}`);
            addLogJobExecution(LogLevel.ERROR, "processBatchForS3Files", `Error processing ticker ${ticker.primary_ticker_eodhd}: ${error.message}`, {}, ticker.id);
        }
    });

    await Promise.all(promises);
    console.log(`[Batch ${batchIndex}/${totalBatches}] Completed processing ${batch.length} tickers`);
}

/**
 * Processes batches sequentially for S3 files general data
 */
async function processBatchesSequentiallyForS3Files(batches: ListOfTickers[][], historyId?: string): Promise<void> {
    let processedTickers = 0;

    for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
        const batch = batches[batchIndex];

        try {
            await processBatchForS3Files(batch, batchIndex + 1, batches.length);
            processedTickers += batch.length;

            // Update progress tracking if historyId is provided
            if (historyId) {
                const {updateJobProgress} = await import("../lib/jobDefinitions");
                const {ObjectId} = await import("mongodb");
                try {
                    await updateJobProgress(new ObjectId(historyId), processedTickers);
                } catch (error) {
                    console.error("Error updating job progress:", error);
                }
            }
        } catch (err: any) {
            console.error(`[Batch ${batchIndex + 1}/${batches.length}] Error processing batch:`, err.message);
            addLogJobExecution(LogLevel.ERROR, "processBatchesSequentiallyForS3Files", "Batch processing error", {
                batchIndex: batchIndex + 1,
                totalBatches: batches.length,
                error: err instanceof Error ? err.message : String(err),
            });

            // Continue processing remaining batches even if one fails
            processedTickers += batch.length;
        }
    }
}

/**
 * Processes batches concurrently with limited concurrency for S3 files general data
 */
async function processBatchesConcurrentlyForS3Files(batches: ListOfTickers[][], maxConcurrency: number, historyId?: string): Promise<void> {
    let processedTickers = 0;

    // Process batches in chunks to limit concurrency
    for (let i = 0; i < batches.length; i += maxConcurrency) {
        const batchChunk = batches.slice(i, i + maxConcurrency);
        const chunkPromises = batchChunk.map(async (batch, chunkIndex) => {
            const batchIndex = i + chunkIndex;

            try {
                await processBatchForS3Files(batch, batchIndex + 1, batches.length);
                return batch.length;
            } catch (err: any) {
                console.error(`[Batch ${batchIndex + 1}/${batches.length}] Error processing concurrent batch:`, err.message);
                addLogJobExecution(LogLevel.ERROR, "processBatchesConcurrentlyForS3Files", "Concurrent batch processing error", {
                    batchIndex: batchIndex + 1,
                    totalBatches: batches.length,
                    error: err instanceof Error ? err.message : String(err),
                });
                return batch.length; // Still count as processed for progress tracking
            }
        });

        // Wait for all batches in this chunk to complete
        const chunkResults = await Promise.all(chunkPromises);
        processedTickers += chunkResults.reduce((sum, count) => sum + count, 0);

        // Update progress tracking if historyId is provided
        if (historyId) {
            const {updateJobProgress} = await import("../lib/jobDefinitions");
            const {ObjectId} = await import("mongodb");
            try {
                await updateJobProgress(new ObjectId(historyId), processedTickers);
            } catch (error) {
                console.error("Error updating job progress:", error);
            }
        }
    }
}

export async function moveFilesController(files: any, keys: any) {
    const fundamentalData = new FundamentalDataRepository();

    for (let i = 0; i < files.length; i++) {
        try {
            await fundamentalData.move(keys[i].Key, "parsed");
            await LogsOfTickersExecute.saveLogToMoveFile(files[i].ticker_internal_id, "File moved successfully");
        } catch (error: any) {
            console.log(error.message);
        }
    }
}

export async function searchFileTickerName(tickers: StatisticsOfTicker[]) {
    const fundamentalData = new FundamentalDataRepository();

    for (let i = 0; i < tickers.length; i++) {
        const ticker = tickers[i];

        const file: iFundamentalData = await fundamentalData.get(`fundamentals_${ticker.symbol_code}_eod.json`);

        const {Highlights: Highlights} = file;

        const dividend = Highlights?.DividendYield || 0;

        ticker.dividend_yield = dividend * 100;

        console.log("dividend ", ticker.dividend_yield, ticker.symbol_code, " saved");

        await ticker.save();
    }
}
