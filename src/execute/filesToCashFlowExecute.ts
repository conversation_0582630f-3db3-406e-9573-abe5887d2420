import {CashFlowRepository} from "../repositories/implements/CashFlowRepository";

export class FilesToCashFlowExecute {
    private cashFlowRepository: CashFlowRepository;
    private responseFile: any;

    constructor(cashFlowRepository: CashFlowRepository, responseFile: any) {
        this.cashFlowRepository = cashFlowRepository;
        this.responseFile = responseFile;
    }

    async execute() {
        await this.cashFlowRepository.getSavedYearly();
        await this.cashFlowRepository.getSavedQuarterly();

        const cashFlow = this.responseFile || {};

        const yearly = cashFlow?.yearly || {};
        const quarterly = cashFlow?.quarterly || {};

        await this.cashFlowRepository.getYearly(yearly);
        await this.cashFlowRepository.getQuarterly(quarterly);

        await this.cashFlowRepository.saveYearly();
        await this.cashFlowRepository.saveQuarterly();

        // Get balance sheet data arrays
        const cashFlowYearly = this.cashFlowRepository.cashFlowYearly || [];
        const cashFlowQuarterly = this.cashFlowRepository.cashFlowQuarterly || [];

        // Calculate quantities
        const quantity_of_cash_flow_year = cashFlowYearly.length;
        const quantity_of_cash_flow_quarter = cashFlowQuarterly.length;

        // Calculate date ranges for yearly data
        let start_of_cash_flow_year = null;
        let end_of_cash_flow_year = null;

        if (cashFlowYearly.length > 0) {
            // First record date (earliest)
            start_of_cash_flow_year = new Date(cashFlowYearly[cashFlowYearly.length - 1]?.document_date) || null;
            // Last record date (latest)
            end_of_cash_flow_year = new Date(cashFlowYearly[0]?.document_date) || null;
        }

        // Calculate date ranges for quarterly data
        let start_of_cash_flow_quarter = null;
        let end_of_cash_flow_quarter = null;

        if (cashFlowQuarterly.length > 0) {
            // First record date (earliest)
            start_of_cash_flow_quarter = new Date(cashFlowQuarterly[cashFlowQuarterly.length - 1]?.document_date) || null;
            // Last record date (latest)
            end_of_cash_flow_quarter = new Date(cashFlowQuarterly[0]?.document_date) || null;
        }

        return {
            quantity_of_cash_flow_year,
            quantity_of_cash_flow_quarter,
            start_of_cash_flow_year,
            end_of_cash_flow_year,
            start_of_cash_flow_quarter,
            end_of_cash_flow_quarter,
        };
    }
}
