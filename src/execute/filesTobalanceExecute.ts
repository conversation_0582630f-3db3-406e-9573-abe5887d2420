import {BalanceSheetRepository} from "../repositories/implements/BalanceSheetRepository";

export class FilesToBalanceExecute {
    private balanceSheetRepository: BalanceSheetRepository;
    private responseFile: any;

    constructor(balanceSheetRepository: BalanceSheetRepository, responseFile: any) {
        this.balanceSheetRepository = balanceSheetRepository;
        this.responseFile = responseFile;
    }

    async execute() {
        await this.balanceSheetRepository.getSavedYearly();
        await this.balanceSheetRepository.getSavedQuarterly();

        const balance_sheet_file = this.responseFile || {};

        const balance_sheet_file_yearly = balance_sheet_file?.yearly || {};
        const balance_sheet_file_quarterly = balance_sheet_file?.quarterly || {};

        await this.balanceSheetRepository.getYearly(balance_sheet_file_yearly);
        await this.balanceSheetRepository.getQuarterly(balance_sheet_file_quarterly);

        await this.balanceSheetRepository.saveYearly();
        await this.balanceSheetRepository.saveQuarterly();

        // Get balance sheet data arrays
        const balanceSheetYearly = this.balanceSheetRepository.balanceSheetYearly || [];
        const balanceSheetQuarterly = this.balanceSheetRepository.balanceSheetQuarterly || [];

        // Calculate quantities
        const quantity_of_balance_sheet_year = balanceSheetYearly.length;
        const quantity_of_balance_sheet_quarter = balanceSheetQuarterly.length;

        // Calculate date ranges for yearly data
        let start_of_balance_sheet_year = null;
        let end_of_balance_sheet_year = null;

        if (balanceSheetYearly.length > 0) {
            // First record date (earliest)
            start_of_balance_sheet_year = new Date(balanceSheetYearly[balanceSheetYearly.length - 1]?.document_date) || null;
            // Last record date (latest)
            end_of_balance_sheet_year = new Date(balanceSheetYearly[0]?.document_date) || null;
        }

        // Calculate date ranges for quarterly data
        let start_of_balance_sheet_quarter = null;
        let end_of_balance_sheet_quarter = null;

        if (balanceSheetQuarterly.length > 0) {
            // First record date (earliest)
            start_of_balance_sheet_quarter = new Date(balanceSheetQuarterly[balanceSheetQuarterly.length - 1]?.document_date) || null;
            // Last record date (latest)
            end_of_balance_sheet_quarter = new Date(balanceSheetQuarterly[0]?.document_date) || null;
        }

        return {
            quantity_of_balance_sheet_year,
            quantity_of_balance_sheet_quarter,
            start_of_balance_sheet_year,
            end_of_balance_sheet_year,
            start_of_balance_sheet_quarter,
            end_of_balance_sheet_quarter,
        };
    }
}
