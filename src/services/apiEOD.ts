import {StatisticsOfAPIController} from "@/controller/statisticsOfAPIController";
import {EndpointType, LatestApi} from "@/entities/consolidated_data/LatestApi";
import axios from "axios";

const {API_URL_EOD, API_KEY_EOD} = process.env;

export const apiEOD = {
    api_key: API_KEY_EOD,
    api: axios.create({
        baseURL: API_URL_EOD,
    }),
    populateTickers(exchange: string, type: string = "stock", fmt: string = "json") {
        const url = `/exchange-symbol-list/${exchange}?api_token=${this.api_key}&type=${type}&fmt=${fmt}`;
        const statisticsOfAPIController = new StatisticsOfAPIController();
        statisticsOfAPIController.createStatisticsOfAPI(new Date(), LatestApi.eodhd, 1, url, EndpointType.symbol_list);
        return this.api.get(url);
    },

    getFundamentaData(stock: string, params: string = "") {
        const url = `/fundamentals/${stock}?api_token=${this.api_key}${params !== "" ? "&" + params : ""}`;
        const statisticsOfAPIController = new StatisticsOfAPIController();
        statisticsOfAPIController.createStatisticsOfAPI(new Date(), LatestApi.eodhd, 10, url), EndpointType.fundamentals;
        return this.api.get(url);
    },

    getDividends(stock: string) {
        const url = `/div/${stock}?api_token=${this.api_key}&fmt=json`;
        const statisticsOfAPIController = new StatisticsOfAPIController();
        statisticsOfAPIController.createStatisticsOfAPI(new Date(), LatestApi.eodhd, 1, url, EndpointType.dividends);
        return this.api.get(url);
    },

    getSplits(stock: string): Promise<any> {
        const url = `/splits/${stock}?api_token=${this.api_key}&fmt=json`;
        const statisticsOfAPIController = new StatisticsOfAPIController();
        statisticsOfAPIController.createStatisticsOfAPI(new Date(), LatestApi.eodhd, 1, url, EndpointType.splits);
        return this.api.get(url);
    },

    getPrice(stock: string, others?: string[]): Promise<any> {
        let url = `/real-time/${stock}?`;
        if (others) {
            url += `s=${others?.toString()}&`;
        }
        url += `api_token=${this.api_key}&fmt=json`;
        const statisticsOfAPIController = new StatisticsOfAPIController();
        statisticsOfAPIController.createStatisticsOfAPI(new Date(), LatestApi.eodhd, 1, url, EndpointType.price);
        return this.api.get(url);
    },

    getPriceHistory(stock: string, period: string = "d", from?: string, to?: string) {
        let url = "";
        if (!from && !to) {
            url = `/eod/${stock}?api_token=${this.api_key}&fmt=json&period=${period}`;
        }
        if (!to) {
            to = new Date().toISOString().split("T")[0];
        }
        url = `/eod/${stock}?api_token=${this.api_key}&fmt=json&from=${from}&to=${to}&period=${period}`;
        const statisticsOfAPIController = new StatisticsOfAPIController();
        statisticsOfAPIController.createStatisticsOfAPI(new Date(), LatestApi.eodhd, 1, url, EndpointType.price);
        return this.api.get(url);
    },
};
