import {startAgenda} from "../lib/agenda";
import {initDatabase} from "../lib/database";

// First initialize the database, then start Agenda
async function startServices() {
    try {
        console.log("Initializing Tiba database connection...");
        const dbInitialized = await initDatabase();

        if (!dbInitialized) {
            console.error("Failed to initialize database. Agenda will not start.");
            process.exit(1);
        }

        console.log("Tiba Database initialized successfully");
        await startAgenda();
    } catch (error) {
        console.error("Failed to start services:", error);
        process.exit(1);
    }
}

// Start the services
startServices();
