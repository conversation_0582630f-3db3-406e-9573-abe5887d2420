import {Model, Optional, DataTypes} from "sequelize";
import {sequelize as conn} from "../../lib/database";
import {iStatisticsOfFilters} from "./iStatisticsOfFilters";

type StatisticsOfFiltersCreationAttributes = Optional<iStatisticsOfFilters, "id" | "created_at" | "updated_at" | "step">;

class StatisticsOfFilters extends Model<iStatisticsOfFilters, StatisticsOfFiltersCreationAttributes> {
    declare id?: number;
    declare indicator_name: string;
    declare created_at?: Date;
    declare updated_at?: Date;
    declare min: number;
    declare max: number;
    declare step?: number;
}

StatisticsOfFilters.init(
    {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
        },
        indicator_name: {
            type: DataTypes.STRING,
            allowNull: true,
        },
        created_at: {
            type: DataTypes.DATE,
            allowNull: true,
        },
        updated_at: {
            type: DataTypes.DATE,
            allowNull: true,
        },
        min: {
            type: DataTypes.DECIMAL(18, 5),
            allowNull: false,
        },
        max: {
            type: DataTypes.DECIMAL(18, 5),
            allowNull: false,
        },
        step: {
            type: DataTypes.DECIMAL(18, 5),
            allowNull: true,
        },
    },
    {
        timestamps: false,
        sequelize: conn,
        tableName: "statistics_of_filter",
        hooks: {
            beforeCreate: (record) => {
                record.dataValues.created_at = new Date();
                record.dataValues.updated_at = new Date();
            },
            beforeUpdate: (record) => {
                record.dataValues.updated_at = new Date();
            },
        },
    },
);

export {StatisticsOfFilters};
