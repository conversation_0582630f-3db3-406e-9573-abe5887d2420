import {Model, Optional, DataTypes} from "sequelize";
import {sequelize as conn} from "../../lib/database";
import {Document_type_year_or_quarter} from "./DocumentTypeYearorQuarterly";
import {iCashFlow} from "./iCashFlow";

type CashFlowcreationAttributes = Optional<
    iCashFlow,
    | "id"
    | "ticker_internal_id"
    | "created_at"
    | "updated_at"
    | "document_date"
    | "currency_symbol"
    | "investments"
    | "change_to_liabilities"
    | "total_cash_flows_from_investing_activities"
    | "net_borrowings"
    | "total_cash_from_financing_activities"
    | "change_to_operating_activities"
    | "net_income"
    | "change_in_cash"
    | "begin_period_cash_flow"
    | "end_period_cash_flow"
    | "total_cash_from_operating_activities"
    | "issuance_of_capital_stock"
    | "depreciation"
    | "other_cash_flows_from_investing_activities"
    | "dividends_paid"
    | "change_to_inventory"
    | "change_to_account_receivables"
    | "sale_purchase_of_stock"
    | "other_cash_flows_from_financing_activities"
    | "change_to_net_income"
    | "capital_expenditures"
    | "change_receivables"
    | "cash_flows_other_operating"
    | "exchange_rate_changes"
    | "cash_and_cash_equivalents_changes"
    | "change_in_working_capital"
    | "stock_based_compensation"
    | "other_non_cash_items"
    | "free_cash_flow"
>;

class CashFlow extends Model<iCashFlow, CashFlowcreationAttributes> {
    declare id?: number;
    declare ticker_internal_id: number;
    declare created_at: Date;
    declare updated_at: Date;
    declare document_current: number;
    declare document_type_year_or_quarter: Document_type_year_or_quarter;
    declare document_date: string;
    declare currency_symbol: string;
    declare investments: number;
    declare change_to_liabilities: number;
    declare total_cash_flows_from_investing_activities: number;
    declare net_borrowings: number;
    declare total_cash_from_financing_activities: number;
    declare change_to_operating_activities: number;
    declare net_income: number;
    declare change_in_cash: number;
    declare begin_period_cash_flow: number;
    declare end_period_cash_flow: number;
    declare total_cash_from_operating_activities: number;
    declare issuance_of_capital_stock: number;
    declare depreciation: number;
    declare other_cash_flows_from_investing_activities: number;
    declare dividends_paid: number;
    declare change_to_inventory: number;
    declare change_to_account_receivables: number;
    declare sale_purchase_of_stock: number;
    declare other_cash_flows_from_financing_activities: number;
    declare change_to_net_income: number;
    declare capital_expenditures: number;
    declare change_receivables: number;
    declare cash_flows_other_operating: number;
    declare exchange_rate_changes: number;
    declare cash_and_cash_equivalents_changes: number;
    declare change_in_working_capital: number;
    declare stock_based_compensation: number;
    declare other_non_cash_items: number;
    declare free_cash_flow: number;
}

CashFlow.init(
    {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
        },
        ticker_internal_id: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        created_at: {
            type: DataTypes.DATE,
            allowNull: true,
        },
        updated_at: {
            type: DataTypes.DATE,
            allowNull: true,
        },
        document_current: {
            type: DataTypes.TINYINT,
            allowNull: false,
        },
        currency_symbol: {
            type: DataTypes.STRING,
            allowNull: true,
        },
        document_type_year_or_quarter: {
            type: DataTypes.ENUM("y", "q"),
            allowNull: false,
        },
        document_date: {
            type: DataTypes.DATEONLY,
            allowNull: true,
        },
        investments: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        change_to_liabilities: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        total_cash_flows_from_investing_activities: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        net_borrowings: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        total_cash_from_financing_activities: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        change_to_operating_activities: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        net_income: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        change_in_cash: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        begin_period_cash_flow: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        end_period_cash_flow: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        total_cash_from_operating_activities: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        issuance_of_capital_stock: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        depreciation: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        other_cash_flows_from_investing_activities: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        dividends_paid: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        change_to_inventory: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        change_to_account_receivables: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        sale_purchase_of_stock: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        other_cash_flows_from_financing_activities: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        change_to_net_income: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        capital_expenditures: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        change_receivables: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        cash_flows_other_operating: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        exchange_rate_changes: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        cash_and_cash_equivalents_changes: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        change_in_working_capital: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        stock_based_compensation: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        other_non_cash_items: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
        free_cash_flow: {
            type: DataTypes.DECIMAL(18, 2),
            allowNull: true,
        },
    },
    {
        timestamps: false,
        sequelize: conn,
        tableName: "fundamentals_cash_flow",
        hooks: {
            beforeCreate: (record, options) => {
                record.dataValues.created_at = new Date();
                record.dataValues.updated_at = new Date();
            },
            beforeUpdate: (record, options) => {
                record.dataValues.updated_at = new Date();
            },
        },
    },
);

export {CashFlow};
