import {Model, Optional, DataTypes} from "sequelize";
import {sequelize as conn} from "../../lib/database";
import {iLogs} from "./iLogs";
import {LatestApi} from "./LatestApi";

type LogsCreationAttributes = Optional<
    iLogs,
    | "id"
    | "ticker_internal_id"
    | "latest_api"
    | "latest_log"
    | "latest_update"
    | "quantity_of_balance_sheet_year"
    | "quantity_of_balance_sheet_quarter"
    | "quantity_of_cash_flow_year"
    | "quantity_of_cash_flow_quarter"
    | "quantity_of_income_statement_year"
    | "quantity_of_income_statement_quarter"
    | "quantity_of_dividends"
    | "quanitty_of_splits"
    | "start_of_balance_sheet_year"
    | "end_of_balance_sheet_year"
    | "start_of_cash_flow_year"
    | "end_of_cash_flow_year"
    | "start_of_income_statement_year"
    | "end_of_income_statement_year"
    | "start_of_balance_sheet_quarter"
    | "end_of_balance_sheet_quarter"
    | "start_of_cash_flow_quarter"
    | "end_of_cash_flow_quarter"
    | "start_of_income_statement_quarter"
    | "end_of_income_statement_quarter"
>;

class Logs extends Model<iLogs, LogsCreationAttributes> {
    declare id?: number;
    declare created_at?: Date;
    declare updated_at?: Date;
    declare ticker_internal_id?: number;
    declare latest_api: LatestApi;
    declare latest_log: string;
    declare latest_update: Date;
    declare quantity_of_balance_sheet_year?: number;
    declare quantity_of_balance_sheet_quarter?: number;
    declare quantity_of_cash_flow_year?: number;
    declare quantity_of_cash_flow_quarter?: number;
    declare quantity_of_income_statement_year?: number;
    declare quantity_of_income_statement_quarter?: number;
    declare quantity_of_dividends?: number;
    declare quanitty_of_splits?: number;
    declare start_of_balance_sheet_year: Date | null;
    declare end_of_balance_sheet_year: Date | null;
    declare start_of_cash_flow_year: Date | null;
    declare end_of_cash_flow_year: Date | null;
    declare start_of_income_statement_year: Date | null;
    declare end_of_income_statement_year: Date | null;
    declare start_of_balance_sheet_quarter: Date | null;
    declare end_of_balance_sheet_quarter: Date | null;
    declare start_of_cash_flow_quarter: Date | null;
    declare end_of_cash_flow_quarter: Date | null;
    declare start_of_income_statement_quarter: Date | null;
    declare end_of_income_statement_quarter: Date | null;
}

Logs.init(
    {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            allowNull: false,
        },
        ticker_internal_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        created_at: {
            type: DataTypes.DATE,
            allowNull: true,
        },
        updated_at: {
            type: DataTypes.DATE,
            allowNull: true,
        },
        latest_api: {
            type: DataTypes.ENUM("eodhd", "twelve_data", "others"),
            allowNull: false,
        },
        latest_log: {
            type: DataTypes.TEXT,
            allowNull: true,
        },
        latest_update: {
            type: DataTypes.DATE,
            allowNull: false,
        },
        quantity_of_balance_sheet_year: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        quantity_of_balance_sheet_quarter: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        quantity_of_cash_flow_year: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        quantity_of_cash_flow_quarter: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        quantity_of_income_statement_year: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        quantity_of_income_statement_quarter: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        quantity_of_dividends: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        quanitty_of_splits: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        start_of_balance_sheet_year: {
            type: DataTypes.DATEONLY,
            allowNull: true,
        },
        end_of_balance_sheet_year: {
            type: DataTypes.DATEONLY,
            allowNull: true,
        },
        start_of_cash_flow_year: {
            type: DataTypes.DATEONLY,
            allowNull: true,
        },
        end_of_cash_flow_year: {
            type: DataTypes.DATEONLY,
            allowNull: true,
        },
        start_of_income_statement_year: {
            type: DataTypes.DATEONLY,
            allowNull: true,
        },
        end_of_income_statement_year: {
            type: DataTypes.DATEONLY,
            allowNull: true,
        },
        start_of_balance_sheet_quarter: {
            type: DataTypes.DATEONLY,
            allowNull: true,
        },
        end_of_balance_sheet_quarter: {
            type: DataTypes.DATEONLY,
            allowNull: true,
        },
        start_of_cash_flow_quarter: {
            type: DataTypes.DATEONLY,
            allowNull: true,
        },
        end_of_cash_flow_quarter: {
            type: DataTypes.DATEONLY,
            allowNull: true,
        },
        start_of_income_statement_quarter: {
            type: DataTypes.DATEONLY,
            allowNull: true,
        },
        end_of_income_statement_quarter: {
            type: DataTypes.DATEONLY,
            allowNull: true,
        },
    },
    {
        timestamps: false,
        sequelize: conn,
        tableName: "logs_of_tickers",
        hooks: {
            beforeCreate: (record) => {
                record.dataValues.created_at = new Date();
                record.dataValues.updated_at = new Date();
            },
            beforeUpdate: (record) => {
                record.dataValues.updated_at = new Date();
            },
        },
    },
);

export {Logs};
