export type FinancialsBalanceSheetType = {
    currency_symbol?: string;
    quarterly?: any;
    yearly?: any;
};

export type FinancialsIncomeStatementType = {
    currency_symbol?: string;
    quarterly?: any;
    yearly?: any;
};

export type FinancialsCashFlowType = {
    currency_symbol?: string;
    quarterly?: any;
    yearly?: any;
};

export type EarningsType = {
    History?: any;
    Trend?: any;
    Annual?: any;
};

export type General = {
    Code?: string;
    Type?: string;
    Name?: string;
    Exchange?: string;
    CurrencyCode?: string;
    CurrencyName?: string;
    CurrencySymbol?: string;
    CountryName?: string;
    CountryISO?: string;
    OpenFigi?: string;
    ISIN?: string;
    LEI?: string | null;
    PrimaryTicker?: string;
    CUSIP?: string;
    CIK?: string;
    EmployerIdNumber?: string;
    FiscalYearEnd?: string;
    IPODate?: string;
    InternationalDomestic?: string;
    Sector?: string;
    Industry?: string;
    GicSector?: string;
    GicGroup?: string;
    GicIndustry?: string;
    GicSubIndustry?: string;
    HomeCategory?: string;
    IsDelisted?: boolean;
    Description?: string;
    Address?: string;
    AddressData?: any;
    Listings?: any;
    Officers?: any;
    Phone?: string;
    WebURL?: string;
    LogoURL?: string;
    FullTimeEmployees?: number;
    UpdatedAt?: string;
};

export type FundamentalDataType = {
    ticker_internal_id?: string;
    General?: General;
    Highlights?: any;
    Valuation?: any;
    SharesStats?: any;
    Technicals?: any;
    SplitsDividends?: any;
    AnalystRatings?: any;
    Holders?: any;
    InsiderTransactions?: any;
    outstandingShares?: any;
    Earnings?: any;
    Financials?: {
        Balance_Sheet?: FinancialsBalanceSheetType;
        Cash_Flow?: FinancialsCashFlowType;
        Income_Statement?: FinancialsIncomeStatementType;
    };
};
