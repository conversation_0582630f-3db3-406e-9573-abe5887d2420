export const ExchangeAPI = {
    USA: {
        NYSE: {
            getExchangeCode: () => "NYSE",
            getAlternativeExchangeCode: () => "US",
            getName: () => "New York Stock Exchange",
            getCurrency: () => "USD",
        },
        NASDAQ: {
            getExchangeCode: () => "NASDAQ",
            getAlternativeExchangeCode: () => "US",
            getName: () => "NASDAQ",
            getCurrency: () => "USD",
        },
    },
    FRANCE: {
        EURONEXT: {
            getExchangeCode: () => "PA",
            getName: () => "Euronext Paris",
            getCurrency: () => "EUR",
        },
    },
    BRAZIL: {
        BOVESPA: {
            getExchangeCode: () => "SA",
            getName: () => "B3 (Brasil Bolsa Balcão)",
            getCurrency: () => "BRL",
        },
    },
    GERMANY: {
        XETRA: {
            getExchangeCode: () => "XETRA",
            getName: () => "XETRA Exchange",
            getCurrency: () => "EUR",
        },
    },
    BELGIUM: {
        EURONEXT: {
            getExchangeCode: () => "BR",
            getName: () => "Euronext Brussels",
            getCurrency: () => "EUR",
        },
    },
    NETHERLANDS: {
        EURONEXT: {
            getExchangeCode: () => "AS",
            getName: () => "Euronext Amsterdam",
            getCurrency: () => "EUR",
        },
    },
    PORTUGAL: {
        EURONEXT: {
            getExchangeCode: () => "LS",
            getName: () => "Euronext Lisbon",
            getCurrency: () => "EUR",
        },
    },
    NORWAY: {
        OSLO: {
            getExchangeCode: () => "OL",
            getName: () => "Oslo Stock Exchange",
            getCurrency: () => "NOK",
        },
    },
    IRELAND: {
        EURONEXT: {
            getExchangeCode: () => "IR",
            getName: () => "Euronext Dublin",
            getCurrency: () => "EUR",
        },
    },
    UNITED_KINGDOM: {
        LONDON: {
            getExchangeCode: () => "LSE",
            getName: () => "London Exchange",
            getCurrency: () => "GBP",
            getAlternativeCurrency: () => "GBX",
        },
    },
};

export interface ResponseTicker {
    Code: string;
    Name: string;
    Country: string;
    Exchange: string;
    Currency: string;
    Type: string;
    Isin: string;
}

export enum TickerType {
    COMMON_STOCK = "Common Stock",
    PREFERRED_STOCK = "Preferred Stock",
    FUND = "FUND",
    MUTUAL_FUND = "Mutual Fund",
    ETF = "ETF",
}

export enum TickerTypeDataBase {
    COMMON_STOCK = "COMMON_STOCK",
    PREFERRED_STOCK = "PREFERRED_STOCK",
    FUND = "FUND",
    MUTUAL_FUND = "MUTUAL_FUND",
    ETF = "ETF",
}

export enum ReasonNotEnable {
    SYMBOL_NOT_FOUND = "Symbol not found",
    PRICE_NOT_FOUND = "Price not found",
    API_404_RESPONSE = "API 404 response",
    OTHER = "Other",
}
