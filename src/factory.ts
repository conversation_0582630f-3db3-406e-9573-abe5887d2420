import {DynamoDBClient} from "@aws-sdk/client-dynamodb";
import {LambdaClient} from "@aws-sdk/client-lambda";
import {S3Client} from "@aws-sdk/client-s3";
import {SQSClient} from "@aws-sdk/client-sqs";

const {REGION: region, IS_OFFLINE: is_offline, STAGE, AWS_ID} = process.env;

export const STATISTICS_QUEUE_URL = `https://sqs.${region}.amazonaws.com/${AWS_ID}/STATISTICS_QUEUE`;
export const PRICE_QUEUE_URL = `https://sqs.${region}.amazonaws.com/${AWS_ID}/PRICE_QUEUE`;
export const BUCKET_NAME = `tiba-invest-api-lambda-${STAGE}-bucket`;

export const BALANCE_SHEET_DYNAMO_TABLE = "balance-sheet";
export const CASH_FLOW_DYNAMO_TABLE = "cash-flow";
export const INCOME_STATEMENT_DYNAMO_TABLE = "income-statement";
export const INVALID_PRICES_DYNAMO_TABLE = "invalid-prices";

const config: any = {
    region,
};

const s3_config: any = {
    ...config,
};

const lambda_config: any = {
    ...config,
};

const dynamo_config: any = {
    ...config,
};

// if(is_offline) {

//     s3_config.forcePathStyle = true,

//     lambda_config.endpoint = 'http://localhost:4566'

//     config.credentials = {
//         accessKeyId: 'test',
//         secretAccessKey: 'test',
//     }

// }

export const lambda = new LambdaClient({...lambda_config});

export const s3 = new S3Client({...s3_config});

export const sqsClient = new SQSClient(config);

export const dynamoClient = new DynamoDBClient(dynamo_config);
