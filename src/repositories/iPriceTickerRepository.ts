import {ListOfTickers} from "@/entities/consolidated_data/ListOfTickers";
import {RealTimePriceEODHD} from "./implements/RealTimePriceDAO";

export interface iPriceTickerRepository {
    setPrice(ticker_price: RealTimePriceEODHD): void;
    setPrices(ticker_prices: RealTimePriceEODHD[]): Promise<void>;
    getPrice(updatedTickers: ListOfTickers[]): Promise<[RealTimePriceEODHD[], RealTimePriceEODHD[]]>;
    getTickers(): void;
}
