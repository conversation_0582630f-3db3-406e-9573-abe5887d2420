import {Op} from "sequelize";
import {ListOfTickers} from "../../entities/consolidated_data/ListOfTickers";
import {StatisticsOfTicker} from "../../entities/consolidated_data/StatisticsOfTickers";
import {iStatisticsOfTicker} from "../../entities/consolidated_data/iStatisticsOfTicker";
import {ApiEOD} from "../../providers/implements/ApiEOD";
import {iPriceTickerRepository} from "../iPriceTickerRepository";
import {addLogJobExecution, getPriceBatchSize, getPriceChunkSize} from "@/lib/agenda";
import {LogLevel} from "@/utils/types/logs/log";
import {RealTimePriceEODHD} from "./RealTimePriceDAO";

export class PriceTickerRepository implements iPriceTickerRepository {
    array_statistics: iStatisticsOfTicker[];
    tickers: ListOfTickers[];
    statisticsToCreate: iStatisticsOfTicker[];
    api: ApiEOD;
    current_valid_prices: RealTimePriceEODHD[];
    current_invalid_prices: RealTimePriceEODHD[];

    constructor() {
        this.api = new ApiEOD();
        this.statisticsToCreate = [];
    }

    async getPrice(updatedTickers: ListOfTickers[], historyId?: string): Promise<[RealTimePriceEODHD[], RealTimePriceEODHD[]]> {
        this.tickers = updatedTickers;
        const valid_prices: RealTimePriceEODHD[] = [];
        const invalid_prices: RealTimePriceEODHD[] = [];

        if (this.tickers.length === 0) {
            return [valid_prices, invalid_prices];
        }

        // Get configurable batch size
        const batchSize = await getPriceBatchSize();
        console.log(`Processing ${this.tickers.length} tickers with batch size: ${batchSize}`);

        addLogJobExecution(LogLevel.INFO, "getPrice", "Starting batch price processing", {
            totalTickers: this.tickers.length,
            batchSize: batchSize,
        });

        // Initialize progress tracking if historyId is provided
        if (historyId) {
            const {initializeJobProgress} = await import("../../lib/jobDefinitions");
            const {ObjectId} = await import("mongodb");
            try {
                await initializeJobProgress(new ObjectId(historyId), this.tickers.length);
            } catch (error) {
                console.error("Error initializing job progress:", error);
            }
        }

        // Process tickers in batches
        const batches = this.createBatches(this.tickers, batchSize);

        // Determine if we should process batches concurrently or sequentially
        const maxConcurrentBatches = Math.min(3, batches.length); // Limit concurrent batches to avoid overwhelming the API
        const shouldProcessConcurrently = batches.length > 1 && maxConcurrentBatches > 1;

        if (shouldProcessConcurrently) {
            console.log(`Processing ${batches.length} batches with max ${maxConcurrentBatches} concurrent batches`);
            await this.processBatchesConcurrently(batches, valid_prices, invalid_prices, maxConcurrentBatches, historyId);
        } else {
            console.log(`Processing ${batches.length} batches sequentially`);
            await this.processBatchesSequentially(batches, valid_prices, invalid_prices, historyId);
        }

        this.current_valid_prices = valid_prices;
        this.current_invalid_prices = invalid_prices;

        addLogJobExecution(LogLevel.INFO, "getPrice", "Price processing completed", {
            totalTickers: this.tickers.length,
            totalValidPrices: valid_prices.length,
            totalInvalidPrices: invalid_prices.length,
            batchesProcessed: batches.length,
        });

        return [valid_prices, invalid_prices];
    }

    /**
     * Creates batches of tickers for batch processing
     */
    private createBatches(tickers: ListOfTickers[], batchSize: number): ListOfTickers[][] {
        const batches: ListOfTickers[][] = [];
        for (let i = 0; i < tickers.length; i += batchSize) {
            batches.push(tickers.slice(i, i + batchSize));
        }
        return batches;
    }

    /**
     * Processes a batch of tickers using the EODHD batch API
     */
    private async processBatch(batch: ListOfTickers[], batchIndex: number, totalBatches: number): Promise<[RealTimePriceEODHD[], RealTimePriceEODHD[]]> {
        const valid_prices: RealTimePriceEODHD[] = [];
        const invalid_prices: RealTimePriceEODHD[] = [];

        if (batch.length === 0) {
            return [valid_prices, invalid_prices];
        }

        // Prepare the primary ticker and others array
        const primaryTicker = batch[0].primary_ticker_eodhd;
        const otherTickers = batch.length > 1 ? batch.slice(1).map((ticker) => ticker.primary_ticker_eodhd) : undefined;

        console.log(`[Batch ${batchIndex}/${totalBatches}] API call for ${batch.length} tickers: ${primaryTicker}${otherTickers ? ` + ${otherTickers.length} others` : ""}`);

        try {
            const response = await this.api.findPrice(primaryTicker, otherTickers);

            // Process the response
            if (Array.isArray(response)) {
                // Multiple tickers response
                for (let i = 0; i < response.length; i++) {
                    const priceData = response[i];
                    const ticker = batch[i]; // Match by index

                    if (priceData.timestamp !== "NA") {
                        valid_prices.push({...priceData, ticker_internal_id: ticker.id});
                    } else {
                        invalid_prices.push({...priceData, ticker_internal_id: ticker.id});
                    }
                }
            } else {
                // Single ticker response
                const ticker = batch[0];
                if (response.timestamp !== "NA") {
                    valid_prices.push({...response, ticker_internal_id: ticker.id});
                } else {
                    invalid_prices.push({...response, ticker_internal_id: ticker.id});
                }
            }
        } catch (err: any) {
            console.error(`[Batch ${batchIndex}/${totalBatches}] API error:`, err.message);
            throw err; // Re-throw to trigger fallback processing
        }

        return [valid_prices, invalid_prices];
    }

    /**
     * Fallback method to process tickers individually when batch processing fails
     */
    private async processBatchIndividually(batch: ListOfTickers[], batchIndex: number): Promise<[RealTimePriceEODHD[], RealTimePriceEODHD[]]> {
        const valid_prices: RealTimePriceEODHD[] = [];
        const invalid_prices: RealTimePriceEODHD[] = [];

        console.log(`[Batch ${batchIndex}] Falling back to individual processing for ${batch.length} tickers`);

        for (let i = 0; i < batch.length; i++) {
            const ticker = batch[i];
            console.log(`[Batch ${batchIndex}] Individual processing: ${ticker.primary_ticker_eodhd} (${i + 1}/${batch.length})`);

            try {
                const response = await this.api.findPrice(ticker.primary_ticker_eodhd);

                if (Array.isArray(response)) {
                    for (let j = 0; j < response.length; j++) {
                        if (response[j].timestamp !== "NA") {
                            valid_prices.push({...response[j], ticker_internal_id: ticker.id});
                        } else {
                            invalid_prices.push({...response[j], ticker_internal_id: ticker.id});
                        }
                    }
                } else {
                    if (response.timestamp !== "NA") {
                        valid_prices.push({...response, ticker_internal_id: ticker.id});
                    } else {
                        invalid_prices.push({...response, ticker_internal_id: ticker.id});
                    }
                }
            } catch (err: any) {
                console.error(`[Batch ${batchIndex}] Individual error for ${ticker.primary_ticker_eodhd}:`, err.message);
                addLogJobExecution(
                    LogLevel.ERROR,
                    "getPrice",
                    "Individual ticker processing error",
                    {
                        error: err instanceof Error ? err.message : String(err),
                        ticker: ticker.primary_ticker_eodhd,
                    },
                    ticker.id,
                );
                continue;
            }
        }

        return [valid_prices, invalid_prices];
    }

    /**
     * Processes batches sequentially (original behavior)
     */
    private async processBatchesSequentially(batches: ListOfTickers[][], valid_prices: RealTimePriceEODHD[], invalid_prices: RealTimePriceEODHD[], historyId?: string): Promise<void> {
        let processedTickers = 0;

        for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
            const batch = batches[batchIndex];
            const batchStartTime = Date.now();

            console.log(`[Batch ${batchIndex + 1}/${batches.length}] Processing ${batch.length} tickers`);

            try {
                const [batchValidPrices, batchInvalidPrices] = await this.processBatch(batch, batchIndex + 1, batches.length);
                valid_prices.push(...batchValidPrices);
                invalid_prices.push(...batchInvalidPrices);

                const batchDuration = Date.now() - batchStartTime;
                console.log(`[Batch ${batchIndex + 1}/${batches.length}] Completed in ${batchDuration}ms`);

                addLogJobExecution(LogLevel.INFO, "getPrice", "Batch processed successfully", {
                    batchIndex: batchIndex + 1,
                    totalBatches: batches.length,
                    batchSize: batch.length,
                    validPrices: batchValidPrices.length,
                    invalidPrices: batchInvalidPrices.length,
                    durationMs: batchDuration,
                });
            } catch (err: any) {
                console.error(`[Batch ${batchIndex + 1}/${batches.length}] Error processing batch:`, err.message);
                addLogJobExecution(LogLevel.ERROR, "getPrice", "Batch processing error", {
                    batchIndex: batchIndex + 1,
                    totalBatches: batches.length,
                    error: err instanceof Error ? err.message : String(err),
                });

                // Fallback to individual processing for failed batch
                const [fallbackValidPrices, fallbackInvalidPrices] = await this.processBatchIndividually(batch, batchIndex + 1);
                valid_prices.push(...fallbackValidPrices);
                invalid_prices.push(...fallbackInvalidPrices);
            }

            // Update progress tracking if historyId is provided
            processedTickers += batch.length;
            if (historyId) {
                const {updateJobProgress} = await import("../../lib/jobDefinitions");
                const {ObjectId} = await import("mongodb");
                try {
                    await updateJobProgress(new ObjectId(historyId), processedTickers);
                } catch (error) {
                    console.error("Error updating job progress:", error);
                }
            }
        }
    }

    /**
     * Processes batches concurrently with limited concurrency
     */
    private async processBatchesConcurrently(
        batches: ListOfTickers[][],
        valid_prices: RealTimePriceEODHD[],
        invalid_prices: RealTimePriceEODHD[],
        maxConcurrency: number,
        historyId?: string,
    ): Promise<void> {
        // Process batches in chunks to limit concurrency
        for (let i = 0; i < batches.length; i += maxConcurrency) {
            const batchChunk = batches.slice(i, i + maxConcurrency);
            const chunkPromises = batchChunk.map(async (batch, chunkIndex) => {
                const batchIndex = i + chunkIndex;
                const batchStartTime = Date.now();

                console.log(`[Batch ${batchIndex + 1}/${batches.length}] Processing ${batch.length} tickers (concurrent)`);

                try {
                    const [batchValidPrices, batchInvalidPrices] = await this.processBatch(batch, batchIndex + 1, batches.length);

                    const batchDuration = Date.now() - batchStartTime;
                    console.log(`[Batch ${batchIndex + 1}/${batches.length}] Completed in ${batchDuration}ms (concurrent)`);

                    addLogJobExecution(LogLevel.INFO, "getPrice", "Concurrent batch processed successfully", {
                        batchIndex: batchIndex + 1,
                        totalBatches: batches.length,
                        batchSize: batch.length,
                        validPrices: batchValidPrices.length,
                        invalidPrices: batchInvalidPrices.length,
                        durationMs: batchDuration,
                    });

                    return {batchValidPrices, batchInvalidPrices};
                } catch (err: any) {
                    console.error(`[Batch ${batchIndex + 1}/${batches.length}] Error processing concurrent batch:`, err.message);
                    addLogJobExecution(LogLevel.ERROR, "getPrice", "Concurrent batch processing error", {
                        batchIndex: batchIndex + 1,
                        totalBatches: batches.length,
                        error: err instanceof Error ? err.message : String(err),
                    });

                    // Fallback to individual processing for failed batch
                    const [fallbackValidPrices, fallbackInvalidPrices] = await this.processBatchIndividually(batch, batchIndex + 1);
                    return {batchValidPrices: fallbackValidPrices, batchInvalidPrices: fallbackInvalidPrices};
                }
            });

            // Wait for all batches in this chunk to complete
            const chunkResults = await Promise.all(chunkPromises);

            // Aggregate results
            for (const result of chunkResults) {
                valid_prices.push(...result.batchValidPrices);
                invalid_prices.push(...result.batchInvalidPrices);
            }
        }
    }

    /**
     * Batch method to set multiple prices efficiently with chunked processing
     */
    async setPrices(tickerPrices: RealTimePriceEODHD[]): Promise<void> {
        if (tickerPrices.length === 0) {
            return;
        }

        console.log(`Setting prices for ${tickerPrices.length} tickers`);
        const startTime = Date.now();

        // For large datasets, process in chunks to avoid timeouts
        const chunkSize = await getPriceChunkSize(); // Get configurable chunk size
        const chunks = this.createPriceChunks(tickerPrices, chunkSize);

        console.log(`Processing ${tickerPrices.length} tickers in ${chunks.length} chunks of ${chunkSize}`);

        let totalUpdates = 0;
        let totalCreates = 0;
        let processedCount = 0;

        try {
            for (let chunkIndex = 0; chunkIndex < chunks.length; chunkIndex++) {
                const chunk = chunks[chunkIndex];
                const chunkStartTime = Date.now();

                console.log(`[Chunk ${chunkIndex + 1}/${chunks.length}] Processing ${chunk.length} tickers`);

                try {
                    const {updatesCount, createsCount} = await this.processPriceChunk(chunk, chunkIndex + 1, chunks.length);
                    totalUpdates += updatesCount;
                    totalCreates += createsCount;
                    processedCount += chunk.length;

                    const chunkDuration = Date.now() - chunkStartTime;
                    console.log(`[Chunk ${chunkIndex + 1}/${chunks.length}] Completed in ${chunkDuration}ms - Updates: ${updatesCount}, Creates: ${createsCount}`);
                } catch (chunkErr: any) {
                    console.error(`[Chunk ${chunkIndex + 1}/${chunks.length}] Error processing chunk:`, chunkErr.message);

                    // Fallback to individual processing for this chunk
                    console.log(`[Chunk ${chunkIndex + 1}/${chunks.length}] Falling back to individual processing...`);
                    for (const tickerPrice of chunk) {
                        try {
                            await this.setPrice(tickerPrice);
                            processedCount++;
                        } catch (individualErr: any) {
                            console.error(`Error setting individual price for ticker ${tickerPrice.ticker_internal_id}:`, individualErr.message);
                        }
                    }
                }
            }

            const duration = Date.now() - startTime;
            console.log(`Batch price setting completed in ${duration}ms for ${processedCount}/${tickerPrices.length} tickers`);

            addLogJobExecution(LogLevel.INFO, "setPrices", "Chunked batch price setting completed", {
                tickersProcessed: processedCount,
                totalTickers: tickerPrices.length,
                chunksProcessed: chunks.length,
                totalUpdates: totalUpdates,
                totalCreates: totalCreates,
                durationMs: duration,
                averageTimePerTicker: Math.round(duration / processedCount),
            });
        } catch (err: any) {
            console.error("Error in chunked batch price setting:", err.message);
            addLogJobExecution(LogLevel.ERROR, "setPrices", "Chunked batch price setting error", {
                error: err instanceof Error ? err.message : String(err),
                tickersCount: tickerPrices.length,
                processedCount: processedCount,
            });

            // Final fallback - process remaining tickers individually
            console.log("Final fallback: processing remaining tickers individually...");
            for (let i = processedCount; i < tickerPrices.length; i++) {
                try {
                    await this.setPrice(tickerPrices[i]);
                } catch (individualErr: any) {
                    console.error(`Error setting individual price for ticker ${tickerPrices[i].ticker_internal_id}:`, individualErr.message);
                }
            }
        }
    }

    /**
     * Creates chunks of ticker prices for processing
     */
    private createPriceChunks(tickerPrices: RealTimePriceEODHD[], chunkSize: number): RealTimePriceEODHD[][] {
        const chunks: RealTimePriceEODHD[][] = [];
        for (let i = 0; i < tickerPrices.length; i += chunkSize) {
            chunks.push(tickerPrices.slice(i, i + chunkSize));
        }
        return chunks;
    }

    /**
     * Processes a single chunk of ticker prices
     */
    private async processPriceChunk(chunk: RealTimePriceEODHD[], _chunkIndex: number, _totalChunks: number): Promise<{updatesCount: number; createsCount: number}> {
        // Group prices by ticker_internal_id for efficient processing
        const priceMap = new Map<number, RealTimePriceEODHD>();
        for (const tickerPrice of chunk) {
            if (tickerPrice.ticker_internal_id) {
                priceMap.set(tickerPrice.ticker_internal_id, tickerPrice);
            }
        }

        // Get all ticker IDs for batch database operations
        const tickerIds = Array.from(priceMap.keys());

        // Batch fetch existing statistics for this chunk
        const existingStatistics = await StatisticsOfTicker.findAll({
            where: {
                ticker_internal_id: {
                    [Op.in]: tickerIds,
                },
            },
        });

        // Create a map for quick lookup
        const statisticsMap = new Map<number, StatisticsOfTicker>();
        for (const stat of existingStatistics) {
            if (stat.ticker_internal_id) {
                statisticsMap.set(stat.ticker_internal_id, stat);
            }
        }

        // Prepare updates and creates
        const updates: Promise<void>[] = [];
        const creates: any[] = [];

        for (const [tickerId, tickerPrice] of priceMap) {
            const {code, close, previousClose} = tickerPrice;

            let price = 0;
            if (close !== "NA" && typeof close === "number") {
                price = close;
            } else if (close === "NA" && previousClose !== "NA" && typeof previousClose === "number") {
                price = previousClose;
            }

            const existingStat = statisticsMap.get(tickerId);
            if (existingStat) {
                // Update existing statistic
                updates.push(existingStat.update({price}).then(() => {}));
            } else {
                // Validate required fields before creating new records
                // Both symbol_code and price are required (NOT NULL in database)
                if (!tickerId || !code || code.trim() === "") {
                    console.warn(`Skipping invalid ticker data: tickerId=${tickerId}, symbol_code=${code}, price=${price}`);
                    addLogJobExecution(LogLevel.WARNING, "processPriceChunk", "Skipped invalid ticker data", {
                        tickerId,
                        symbol_code: code,
                        price,
                        reason: "Missing required fields (ticker_internal_id or symbol_code)",
                    });
                    continue; // Skip this record
                }

                creates.push({
                    price,
                    symbol_code: code.trim(),
                    ticker_internal_id: tickerId,
                });
            }
        }

        // Execute updates in smaller batches to avoid overwhelming the database
        if (updates.length > 0) {
            const updateBatchSize = 50; // Reduce from 100 to 50 updates at a time
            for (let i = 0; i < updates.length; i += updateBatchSize) {
                const updateBatch = updates.slice(i, i + updateBatchSize);
                try {
                    await Promise.all(updateBatch);
                    // Add a small delay between batches to prevent connection pool exhaustion
                    if (i + updateBatchSize < updates.length) {
                        await new Promise((resolve) => setTimeout(resolve, 500));
                    }
                } catch (updateErr: any) {
                    console.error("Error in update batch:", updateErr.message);
                    addLogJobExecution(LogLevel.ERROR, "processPriceChunk", "Update batch error", {
                        error: updateErr.message,
                        batchSize: updateBatch.length,
                    });
                    // Add a longer delay after errors
                    await new Promise((resolve) => setTimeout(resolve, 2000));
                }
            }
        }

        // Bulk create new statistics with validation error handling
        if (creates.length > 0) {
            try {
                await StatisticsOfTicker.bulkCreate(creates, {
                    validate: true,
                    ignoreDuplicates: true, // Ignore duplicate entries to prevent conflicts
                });
            } catch (createErr: any) {
                // Extract more detailed error information
                const errorDetails = {
                    message: createErr.message,
                    name: createErr.name,
                    sql: createErr.sql || null,
                    parameters: createErr.parameters || null,
                    validationErrors: createErr.errors || null,
                };

                console.error("Validation error in bulkCreate:", JSON.stringify(errorDetails, null, 2));
                addLogJobExecution(LogLevel.ERROR, "processPriceChunk", "Bulk create validation error", {
                    error: errorDetails,
                    recordsCount: creates.length,
                    sampleRecord: creates[0] || null,
                });

                // Fallback: try to create records individually to identify problematic ones
                console.log("Falling back to individual record creation...");
                let individualCreates = 0;
                for (const record of creates) {
                    try {
                        await StatisticsOfTicker.create(record);
                        individualCreates++;
                    } catch (individualErr: any) {
                        const individualErrorDetails = {
                            message: individualErr.message,
                            name: individualErr.name,
                            sql: individualErr.sql || null,
                            validationErrors: individualErr.errors || null,
                        };
                        console.error(`Failed to create individual record for ticker ${record.ticker_internal_id}:`, JSON.stringify(individualErrorDetails, null, 2));
                        addLogJobExecution(LogLevel.ERROR, "processPriceChunk", "Individual create error", {
                            error: individualErrorDetails,
                            record: record,
                        });
                    }
                }
                return {
                    updatesCount: updates.length,
                    createsCount: individualCreates,
                };
            }
        }

        return {
            updatesCount: updates.length,
            createsCount: creates.length,
        };
    }

    async setPrice(tickerPrice: RealTimePriceEODHD): Promise<void> {
        const {ticker_internal_id, code, close, previousClose} = tickerPrice;
        try {
            // Validate required fields
            if (!ticker_internal_id) {
                console.warn("Skipping setPrice: missing ticker_internal_id");
                return;
            }

            let price = 0;
            if (close !== "NA" && typeof close === "number") {
                price = close;
            }
            if (close === "NA" && previousClose !== "NA" && typeof previousClose === "number") {
                price = previousClose;
            }

            const statistic = await StatisticsOfTicker.findOne({
                where: {ticker_internal_id: ticker_internal_id},
            });

            if (statistic) {
                statistic.price = price;
                await statistic.save();
            } else {
                const list_of_ticker = await ListOfTickers.findOne({
                    where: {id: ticker_internal_id},
                });

                if (list_of_ticker) {
                    // Validate required fields before creating
                    if (!code || code.trim() === "") {
                        console.warn(`Skipping create for ticker ${ticker_internal_id}: missing symbol_code`);
                        addLogJobExecution(LogLevel.WARNING, "setPrice", "Skipped create due to missing symbol_code", {
                            ticker_internal_id,
                            code,
                            price,
                        });
                        return;
                    }

                    await StatisticsOfTicker.create({
                        price,
                        symbol_code: code.trim(),
                        ticker_internal_id: list_of_ticker.id || 0,
                    });
                }
            }
        } catch (err: any) {
            const errorDetails = {
                message: err.message,
                name: err.name,
                sql: err.sql || null,
                validationErrors: err.errors || null,
            };
            console.error("Validation error in setPrice:", JSON.stringify(errorDetails, null, 2));
            addLogJobExecution(
                LogLevel.ERROR,
                "setPrice",
                "Error when try to set price",
                {
                    error: errorDetails,
                    ticker_internal_id,
                    code,
                    close,
                    previousClose,
                },
                ticker_internal_id || 0,
            );
        }
    }

    async getTickers(listOfTickersId?: number[]): Promise<void> {
        const params: any = {};

        const date = new Date();
        const minutes = date.getMinutes();

        const tickers_count = await ListOfTickers.count({
            where: {
                is_enable: 1,
            },
        });

        const mod = tickers_count % 6 || 0;
        const limit = Math.trunc(tickers_count / 6) + mod;

        const page = minutes < 10 ? 0 : Math.floor(minutes / 10);

        if (listOfTickersId && listOfTickersId.length > 0) {
            params.where = {
                id: {
                    [Op.in]: listOfTickersId,
                },
                is_enable: 1,
            };
        } else {
            params.where = {
                is_enable: 1,
            };
            params.order = [["id", "ASC"]];
            params.attributes = ["primary_ticker_eodhd", "id", "symbol_code"];
            params.offset = page * limit;
            params.limit = limit;
        }

        const tickers = await ListOfTickers.findAll(params);

        this.tickers = tickers;
    }
}
