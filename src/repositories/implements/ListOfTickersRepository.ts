import {Op} from "sequelize";

import {ListOfTickers, tickers, TickersInput} from "../../entities/consolidated_data/ListOfTickers";
import {IListOfTickersRepository} from "../IListOfTickersRepository";
import {LogsController} from "@/controller/logsController";
import {addLogJobExecution} from "@/lib/agenda";
import {LogLevel} from "@/utils/types/logs/log";
import {Sector} from "@/entities/consolidated_data/Sectors";

export class ListOfTickersRepository implements IListOfTickersRepository {
    async bulkCreateOrUpdate(tickersData: Partial<ListOfTickers>[]): Promise<ListOfTickers[]> {
        addLogJobExecution(LogLevel.INFO, "bulkCreateOrUpdate", "Creating or updating tickers", {tickersDataLength: tickersData.length});

        const startTime = Date.now();
        const results: ListOfTickers[] = [];
        const lastTicker = await ListOfTickers.findOne({
            order: [["id", "DESC"]],
        });
        let nextId = lastTicker && lastTicker.id ? lastTicker.id + 1 : 1;
        console.log("This is the nextId: ", nextId);
        for (const tickerData of tickersData) {
            // Try to find existing ticker by primary_ticker_eodhd
            let existingTicker = await ListOfTickers.findOne({
                where: {primary_ticker_eodhd: tickerData.primary_ticker_eodhd},
            });

            // For German stocks, if not found with XETR, try with BE (Berlin)
            if (!existingTicker && tickerData.primary_ticker_eodhd && tickerData.primary_ticker_eodhd.endsWith(".XETRA")) {
                const berlinTicker = tickerData.primary_ticker_eodhd.replace(".XETRA", ".BE");
                existingTicker = await ListOfTickers.findOne({
                    where: {primary_ticker_eodhd: berlinTicker},
                });
            }

            if (existingTicker) {
                // Update existing ticker
                Object.assign(existingTicker, tickerData);
                existingTicker.updated_at = new Date();
                await existingTicker.save();
                results.push(existingTicker);
                const logsController = new LogsController();
                if (existingTicker.id) {
                    logsController.tickerUpdatedEODHD(existingTicker.id, "Ticker updated");
                }
            } else {
                tickerData.id = nextId;
                tickerData.created_at = new Date();
                tickerData.updated_at = new Date();
                tickerData.is_enable = 1;
                const newTicker = await ListOfTickers.create(tickerData as TickersInput);
                results.push(newTicker);
                nextId++;
                const logsController = new LogsController();
                logsController.tickerUpdatedEODHD(tickerData.id, "Ticker created");
            }
        }
        console.log(`Created or updated ${results.length} tickers in ${Date.now() - startTime}ms`);

        addLogJobExecution(LogLevel.INFO, "bulkCreateOrUpdate", `Created or updated ${results.length} tickers in ${Date.now() - startTime}ms`, {resultsLength: results.length});

        return results;
    }

    async getOutdatedTickers(daysThreshold: number = 7): Promise<ListOfTickers[]> {
        console.log(`Retrieving tickers not updated in the last ${daysThreshold} days`);
        const startTime = Date.now();

        const thresholdDate = new Date();
        thresholdDate.setDate(thresholdDate.getDate() - daysThreshold);
        console.log(`Threshold date: ${thresholdDate.toISOString()}`);

        const params: any = {
            attributes: ["primary_ticker_eodhd", "id"],
            order: [["id", "ASC"]],
            where: {
                is_enable: 1,
                [Op.or]: [{fundamental_data_last_updated: {[Op.lt]: thresholdDate}}, {fundamental_data_last_updated: null}],
            },
        };

        const outdatedTickers = await tickers.findAll(params);
        const executionTime = Date.now() - startTime;

        console.log(`Found ${outdatedTickers.length} outdated tickers in ${executionTime}ms`);
        return outdatedTickers;
    }

    async getSectors() {
        const sectors = await Sector.findAll({
            attributes: ["id", "en"],
        });

        return sectors;
    }

    async updateSector(id: number, sector: string, sectors: Sector[]) {
        console.log(`Updating sector for ticker ${id} to ${sector}`);
        const sectorId = sectors.find((s) => s.en === sector)?.id;
        await ListOfTickers.update(
            {
                sector_id: sectorId,
            },
            {
                where: {
                    id,
                },
            },
        );
    }
}
