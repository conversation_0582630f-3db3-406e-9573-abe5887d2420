import {sequelize} from "../../lib/database";
import {StatisticsOfFilters} from "../../entities/consolidated_data/StatisticsOfFilters";
import {StatisticsOfTicker} from "../../entities/consolidated_data/StatisticsOfTickers";
import {StatisticsOfFiltersDAO} from "./StatisticsOfFiltersDAO";

export class StatisticsOfFiltersRepository {
    filters: any;

    async getFiltersValues() {
        const statistics = await StatisticsOfTicker.findAll({
            attributes: [
                [sequelize.fn("min", sequelize.col("price")), "price_min"],
                [sequelize.fn("max", sequelize.col("price")), "price_max"],

                [sequelize.fn("min", sequelize.col("dividend_yield")), "dividend_yield_min"],
                [sequelize.fn("max", sequelize.col("dividend_yield")), "dividend_yield_max"],

                [sequelize.fn("min", sequelize.col("eps_current")), "eps_current_min"],
                [sequelize.fn("max", sequelize.col("eps_current")), "eps_current_max"],

                [sequelize.fn("min", sequelize.col("eps_last_year")), "eps_last_year_min"],
                [sequelize.fn("max", sequelize.col("eps_last_year")), "eps_last_year_max"],

                [sequelize.fn("min", sequelize.col("pe")), "pe_min"],
                [sequelize.fn("max", sequelize.col("pe")), "pe_max"],

                [sequelize.fn("min", sequelize.col("peg_ratio")), "peg_ratio_min"],
                [sequelize.fn("max", sequelize.col("peg_ratio")), "peg_ratio_max"],

                [sequelize.fn("min", sequelize.col("bvps")), "bvps_min"],
                [sequelize.fn("max", sequelize.col("bvps")), "bvps_max"],

                [sequelize.fn("min", sequelize.col("price_to_book")), "price_to_book_min"],
                [sequelize.fn("max", sequelize.col("price_to_book")), "price_to_book_max"],

                [sequelize.fn("min", sequelize.col("ebitda")), "ebitda_min"],
                [sequelize.fn("max", sequelize.col("ebitda")), "ebitda_max"],

                [sequelize.fn("min", sequelize.col("ebitda_margin")), "ebitda_margin_min"],
                [sequelize.fn("max", sequelize.col("ebitda_margin")), "ebitda_margin_max"],

                [sequelize.fn("min", sequelize.col("ebit")), "ebit_min"],
                [sequelize.fn("max", sequelize.col("ebit")), "ebit_max"],

                [sequelize.fn("min", sequelize.col("operation_margin")), "operation_margin_min"],
                [sequelize.fn("max", sequelize.col("operation_margin")), "operation_margin_max"],

                [sequelize.fn("min", sequelize.col("net_margin")), "net_margin_min"],
                [sequelize.fn("max", sequelize.col("net_margin")), "net_margin_max"],

                [sequelize.fn("min", sequelize.col("gross_margin")), "gross_margin_min"],
                [sequelize.fn("max", sequelize.col("gross_margin")), "gross_margin_max"],

                [sequelize.fn("min", sequelize.col("price_working_capital_share")), "price_working_capital_share_min"],
                [sequelize.fn("max", sequelize.col("price_working_capital_share")), "price_working_capital_share_max"],

                [sequelize.fn("min", sequelize.col("ebit_ratio")), "ebit_ratio_min"],
                [sequelize.fn("max", sequelize.col("ebit_ratio")), "ebit_ratio_max"],

                [sequelize.fn("min", sequelize.col("net_debt")), "net_debt_min"],
                [sequelize.fn("max", sequelize.col("net_debt")), "net_debt_max"],

                [sequelize.fn("min", sequelize.col("net_debt_ebit")), "net_debt_ebit_min"],
                [sequelize.fn("max", sequelize.col("net_debt_ebit")), "net_debt_ebit_max"],

                [sequelize.fn("min", sequelize.col("net_debt_ebitda")), "net_debt_ebitda_min"],
                [sequelize.fn("max", sequelize.col("net_debt_ebitda")), "net_debt_ebitda_max"],

                [sequelize.fn("min", sequelize.col("market_capitalization")), "market_capitalization_min"],
                [sequelize.fn("max", sequelize.col("market_capitalization")), "market_capitalization_max"],

                [sequelize.fn("min", sequelize.col("ev_ebit")), "ev_ebit_min"],
                [sequelize.fn("max", sequelize.col("ev_ebit")), "ev_ebit_max"],

                [sequelize.fn("min", sequelize.col("net_debt_shareholders_equity")), "net_debt_shareholders_equity_min"],
                [sequelize.fn("max", sequelize.col("net_debt_shareholders_equity")), "net_debt_shareholders_equity_max"],

                [sequelize.fn("min", sequelize.col("price_sales_ratio")), "price_sales_ratio_min"],
                [sequelize.fn("max", sequelize.col("price_sales_ratio")), "price_sales_ratio_max"],

                [sequelize.fn("min", sequelize.col("roe")), "roe_min"],
                [sequelize.fn("max", sequelize.col("roe")), "roe_max"],

                [sequelize.fn("min", sequelize.col("roa")), "roa_min"],
                [sequelize.fn("max", sequelize.col("roa")), "roa_max"],

                [sequelize.fn("min", sequelize.col("roic")), "roic_min"],
                [sequelize.fn("max", sequelize.col("roic")), "roic_max"],

                [sequelize.fn("min", sequelize.col("current_ratio")), "current_ratio_min"],
                [sequelize.fn("max", sequelize.col("current_ratio")), "current_ratio_max"],

                [sequelize.fn("min", sequelize.col("shareholder_equity_ratio")), "shareholder_equity_ratio_min"],
                [sequelize.fn("max", sequelize.col("shareholder_equity_ratio")), "shareholder_equity_ratio_max"],

                [sequelize.fn("min", sequelize.col("total_debt_to_total_assets_ratio")), "total_debt_to_total_assets_ratio_min"],
                [sequelize.fn("max", sequelize.col("total_debt_to_total_assets_ratio")), "total_debt_to_total_assets_ratio_max"],

                [sequelize.fn("min", sequelize.col("asset_turnover")), "asset_turnover_min"],
                [sequelize.fn("max", sequelize.col("asset_turnover")), "asset_turnover_max"],

                [sequelize.fn("min", sequelize.col("payout_ratio")), "payout_ratio_min"],
                [sequelize.fn("max", sequelize.col("payout_ratio")), "payout_ratio_max"],
            ],
        });

        const [data] = statistics || {};

        const filters = this.formatFitlersdata(data?.dataValues || {});

        this.filters = filters;
    }

    formatFitlersdata(filters: any): StatisticsOfFiltersDAO {
        const keys = Object.keys(filters);

        const values: any = {};

        for (let i = 0; i < keys.length; i++) {
            const key = keys[i];

            const name = key.replace("_min", "").replace("_max", "");

            if (!(name in values)) {
                values[`${name}`] = {};
            }

            if (`${key}`.includes("min")) {
                values[`${name}`].min = filters[`${key}`] || 0;
            }

            if (`${key}`.includes("max")) {
                values[`${name}`].max = filters[`${key}`] || 10000000000000;
            }
        }

        return values;
    }

    async saveFilters() {
        const keys = Object.keys(this.filters);

        for (let i = 0; i < keys.length; i++) {
            const attribute = keys[i];

            const filter = await StatisticsOfFilters.findOne({
                where: {
                    indicator_name: attribute,
                },
            });

            if (filter) {
                filter.min = this.filters[`${attribute}`]?.min;
                filter.max = this.filters[`${attribute}`]?.max;

                await filter.save();
            } else {
                await StatisticsOfFilters.create({
                    indicator_name: attribute,
                    min: this.filters[`${attribute}`]?.min,
                    max: this.filters[`${attribute}`]?.max,
                });
            }
        }
    }
}
