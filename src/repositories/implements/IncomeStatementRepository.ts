import {IncomeStatement} from "../../entities/consolidated_data/IncomeStatement";
import {Document_type_year_or_quarter} from "../../entities/consolidated_data/DocumentTypeYearorQuarterly";
import _, {snakeCase} from "lodash";
import {IncomeStatementDAO} from "./IncomeStatementDAO";
import {iIncomeStatementDAO} from "./../iIncomeStatementDAO";

export class IncomeStatementRepository {
    saved_yearly: string[];
    saved_quarterly: string[];
    incomeStatementYearly: IncomeStatementDAO[];
    incomeStatementQuarterly: IncomeStatementDAO[];
    ticker_internal_id: number;
    files_dates_yearly: string[];
    files_dates_quarterly: string[];

    constructor(ticker_internal_id: number) {
        this.ticker_internal_id = ticker_internal_id;
    }

    async getSavedYearly(): Promise<void> {
        const saved_yearly = await IncomeStatement.findAll({
            where: {
                ticker_internal_id: this.ticker_internal_id,
                document_type_year_or_quarter: Document_type_year_or_quarter.y,
            },
            attributes: ["document_date"],
        });

        const data: string[] = [];

        for (let i = 0; i < saved_yearly.length; i++) {
            data.push(saved_yearly[i].document_date);
        }

        this.saved_yearly = data;
    }

    async getSavedQuarterly(): Promise<void> {
        const saved_quarterly = await IncomeStatement.findAll({
            where: {
                ticker_internal_id: this.ticker_internal_id,
                document_type_year_or_quarter: Document_type_year_or_quarter.q,
            },
            attributes: ["document_date"],
        });

        const data: string[] = [];

        for (let i = 0; i < saved_quarterly.length; i++) {
            data.push(saved_quarterly[i].document_date);
        }

        this.saved_quarterly = data;
    }

    async getYearly(incomeStatementYearly: any) {
        try {
            const ticker_internal_id = this.ticker_internal_id;

            const data: IncomeStatementDAO[] = [];

            const keys = Object.keys(incomeStatementYearly);

            this.files_dates_yearly = keys;

            for (let i = 0; i < keys.length; i++) {
                const current_key = keys[i];

                const incomeStatement = incomeStatementYearly[current_key];

                const snakeCasedObject = Object.keys(incomeStatement).reduce(
                    (result, key) => ({
                        ...result,
                        [snakeCase(key)]: incomeStatement[key],
                    }),
                    {},
                );

                const params: iIncomeStatementDAO = {
                    ticker_internal_id,
                    ...snakeCasedObject,
                    document_type_year_or_quarter: Document_type_year_or_quarter.y,
                    date: current_key,
                };

                const incomeStatementData = new IncomeStatementDAO(params);

                data.push(incomeStatementData);
            }

            this.incomeStatementYearly = data;
        } catch (error: any) {
            throw Error("Error when try to get IncomeStatement Yearly " + error.message);
        }
    }

    async getQuarterly(incomeStatementQuarterly: any) {
        try {
            const ticker_internal_id = this.ticker_internal_id;

            const data: IncomeStatementDAO[] = [];

            const keys = Object.keys(incomeStatementQuarterly);

            this.files_dates_quarterly = keys;

            for (let i = 0; i < keys.length; i++) {
                const current_key = keys[i];

                const incomeStatement = incomeStatementQuarterly[current_key];

                const snakeCasedObject = Object.keys(incomeStatement).reduce(
                    (result, key) => ({
                        ...result,
                        [snakeCase(key)]: incomeStatement[key],
                    }),
                    {},
                );

                const params: iIncomeStatementDAO = {
                    ...snakeCasedObject,
                    ticker_internal_id,
                    document_type_year_or_quarter: Document_type_year_or_quarter.q,
                    date: current_key,
                };

                const incomeStatementData = new IncomeStatementDAO(params);

                data.push(incomeStatementData);
            }

            this.incomeStatementQuarterly = data;
        } catch (error: any) {
            throw Error("Error when try to get Income Statement Quarterly " + error.message);
        }
    }

    async saveYearly() {
        try {
            let toSave: IncomeStatementDAO[] = [];

            if (this.saved_yearly.length === 0 && this.incomeStatementYearly.length > 0) {
                toSave = this.incomeStatementYearly;
                toSave[0].document_current = 1;
            } else {
                const delta = _.difference(this.files_dates_yearly, this.saved_yearly);

                if (delta.length > 0) {
                    const income = await IncomeStatement.findOne({
                        where: {
                            document_current: 1,
                            ticker_internal_id: this.ticker_internal_id,
                            document_type_year_or_quarter: Document_type_year_or_quarter.y,
                        },
                    });

                    let bigger_date_file = this.files_dates_yearly[0];

                    for (let i = 0; i < this.incomeStatementYearly.length; i++) {
                        if (delta?.includes(this.incomeStatementYearly[i].document_date)) {
                            if (this.incomeStatementYearly[i].document_date === bigger_date_file) {
                                this.incomeStatementYearly[i].document_current = 1;
                            }

                            toSave.push(this.incomeStatementYearly[i]);
                        }
                    }

                    if (income && income.document_date < bigger_date_file) {
                        income.document_current = 0;
                        await income.save();
                    }
                }
            }

            if (toSave.length > 0) {
                await IncomeStatement.bulkCreate(toSave); //cast to interface BalanceSheet
                console.log("IncomeStatement yearly saved");
            }
        } catch (error: any) {
            console.log("Error when try to save Income statement yearly", error.mesage);
        }
    }

    async saveQuarterly() {
        try {
            let toSave: IncomeStatementDAO[] = [];

            if (this.saved_quarterly.length === 0 && this.incomeStatementQuarterly.length > 0) {
                toSave = this.incomeStatementQuarterly;
                toSave[0].document_current = 1;
            } else {
                const delta = _.difference(this.files_dates_quarterly, this.saved_quarterly);

                if (delta.length > 0) {
                    const income = await IncomeStatement.findOne({
                        where: {
                            document_current: 1,
                            ticker_internal_id: this.ticker_internal_id,
                            document_type_year_or_quarter: Document_type_year_or_quarter.q,
                        },
                    });

                    let bigger_date_file = this.files_dates_quarterly[0];

                    for (let i = 0; i < this.incomeStatementQuarterly.length; i++) {
                        if (delta?.includes(this.incomeStatementQuarterly[i].document_date)) {
                            if (this.incomeStatementQuarterly[i].document_date === bigger_date_file) {
                                this.incomeStatementQuarterly[i].document_current = 1;
                            }

                            toSave.push(this.incomeStatementQuarterly[i]);
                        }
                    }

                    if (income && income.document_date < bigger_date_file) {
                        income.document_current = 0;
                        await income.save();
                    }
                }
            }

            if (toSave.length > 0) {
                await IncomeStatement.bulkCreate(toSave); //cast to interface BalanceSheet
                console.log("IncomeStatement quarterly saved");
            }
        } catch (error: any) {
            console.log("Error when try to save income statement quarterly", error.mesage);
        }
    }
}
