import {Latest<PERSON><PERSON>} from "@/entities/consolidated_data/LatestApi";
import {StatisticsOfAPI} from "../../entities/consolidated_data/StatisticsOfAPI";
import {Op} from "sequelize";
import {sequelize} from "../../lib/database";

export class StatisticsOfAPIRepository {
    async create(_statisticsAPI: Partial<StatisticsOfAPI>) {
        try {
            // Remove manual ID assignment - let the database auto-increment handle it
            // This prevents potential race conditions and duplicate key errors
            const dataToCreate = {..._statisticsAPI};
            delete dataToCreate.id; // Remove id to let auto-increment work

            await StatisticsOfAPI.create(dataToCreate as StatisticsOfAPI);
        } catch (error: any) {
            // Enhanced error logging to identify the validation issue
            const errorDetails = {
                message: error.message,
                name: error.name,
                sql: error.sql || null,
                validationErrors: error.errors || null,
                data: _statisticsAPI,
            };
            console.error("Validation error in StatisticsOfAPI.create:", JSON.stringify(errorDetails, null, 2));
        }
    }

    async getStatisticsOfAPI(request_date: Date, latest_api: LatestApi) {
        try {
            const statistics = await StatisticsOfAPI.findAll({
                attributes: ["request_date", "latest_api", "api_credit_consumption", "request_url", "endpoint_type"],
                where: {
                    request_date: {
                        [Op.eq]: request_date,
                    },
                    latest_api,
                },
                order: [["request_date", "DESC"]],
            });

            return statistics;
        } catch (error: any) {
            console.log(error.message);
        }
    }

    async getStatisticsOfAPICurrentComsumption(latest_api: LatestApi) {
        try {
            const today = new Date();
            const todayDateString = today.toISOString().split("T")[0]; // Get YYYY-MM-DD format

            const result = await StatisticsOfAPI.findOne({
                attributes: [[sequelize.fn("SUM", sequelize.col("api_credit_consumption")), "todayApiCredits"]],
                where: {
                    [Op.and]: [sequelize.where(sequelize.fn("DATE", sequelize.col("request_date")), todayDateString), {latest_api}],
                },
                raw: true,
            });

            return result;
        } catch (error: any) {
            console.log(error.message);
        }
    }

    async getApiCreditsForPastDays(days: number, latest_api: LatestApi = LatestApi.eodhd) {
        try {
            const endDate = new Date();
            endDate.setHours(23, 59, 59, 999);

            const startDate = new Date();
            startDate.setDate(startDate.getDate() - days + 1);
            startDate.setHours(0, 0, 0, 0);

            const statistics = await StatisticsOfAPI.findAll({
                attributes: [
                    [sequelize.fn("DATE", sequelize.col("request_date")), "date"],
                    [sequelize.fn("SUM", sequelize.col("api_credit_consumption")), "usage"],
                ],
                where: {
                    request_date: {
                        [Op.gte]: startDate,
                        [Op.lte]: endDate,
                    },
                    latest_api,
                },
                group: [sequelize.fn("DATE", sequelize.col("request_date"))],
                order: [[sequelize.fn("DATE", sequelize.col("request_date")), "ASC"]],
                raw: true,
            });

            return statistics;
        } catch (error: any) {
            console.log(error.message);
            return [];
        }
    }
}
