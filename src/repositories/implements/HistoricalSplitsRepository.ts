import _ from "lodash";
import {HistoricalSplits} from "../../entities/consolidated_data/HistoricalSplits";
import {iSplitsDAO} from "../iSplitsDAO";
import {SplitsDAO} from "./SplitsDao";
import {addLogJobExecution} from "@/lib/agenda";
import {LogLevel} from "@/utils/types/logs/log";
import e from "cors";

export class HistoricalSplitsRepository {
    ticker_internal_id: number;

    constructor(ticket_internal_id: number) {
        this.ticker_internal_id = ticket_internal_id;
    }

    async saveSplitsData(eod_splits: iSplitsDAO[]) {
        try {
            if (eod_splits && eod_splits.length > 0) {
                addLogJobExecution(LogLevel.INFO, "saveSplitsData", "Saving splits for ticker", {eod_splitsLength: eod_splits.length}, this.ticker_internal_id);
                const splits = await HistoricalSplits.findAll({
                    where: {
                        ticker_internal_id: this.ticker_internal_id,
                    },
                    attributes: ["document_date"],
                });
                let toSave: SplitsDAO[] = [];
                if (splits && splits.length > 0) {
                    const splits_date_strapi = splits.map((split) => split.document_date);
                    const splits_date_eod = eod_splits.map((split) => split.date);
                    const diff = _.difference(splits_date_eod, splits_date_strapi);
                    for (let i = 0; i < eod_splits.length; i++) {
                        if (diff.includes(eod_splits[i].date)) {
                            const historical_split = new SplitsDAO(eod_splits[i], this.ticker_internal_id);
                            toSave.push(historical_split);
                        }
                    }
                    addLogJobExecution(
                        LogLevel.INFO,
                        "saveSplitsData",
                        "Splits to save",
                        {splits_date_strapiLength: splits_date_strapi.length, splits_date_eodLength: splits_date_eod.length, diffLength: diff.length, toSaveLength: toSave.length},
                        this.ticker_internal_id,
                    );
                } else {
                    for (let i = 0; i < eod_splits.length; i++) {
                        const historical_split = new SplitsDAO(eod_splits[i], this.ticker_internal_id);
                        toSave.push(historical_split);
                    }
                }
                if (toSave.length > 0) {
                    HistoricalSplits.bulkCreate(toSave);
                    addLogJobExecution(LogLevel.INFO, "saveSplitsData", "Saved splits for ticker", {toSaveLength: toSave.length}, this.ticker_internal_id);
                } else {
                    addLogJobExecution(LogLevel.INFO, "saveSplitsData", "No splits to save for ticker", {}, this.ticker_internal_id);
                }
            }
        } catch (error: any) {
            addLogJobExecution(LogLevel.ERROR, "saveSplitsData", "Error when try to save splits", {error: error instanceof Error ? error.message : String(error)}, this.ticker_internal_id);
            throw new Error("Error when try to save splits " + error.message);
        }
    }
}
