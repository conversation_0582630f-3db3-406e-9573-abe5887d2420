import _ from "lodash";
import {HistoricalDividends} from "../../entities/consolidated_data/HistoricalDividends";
import {iHistoryDividendsRepository} from "../iHistoryDividendsRepository";
import {DividendsEODHDAO} from "./DIvidendsEODHDDAO";
import {addLogJobExecution} from "@/lib/agenda";
import {LogLevel} from "@/utils/types/logs/log";

export class HistoryDividendsEODRepository implements iHistoryDividendsRepository {
    dividends: HistoricalDividends[];
    ticker_internal_id: number;
    historicalJson: DividendsEODHDAO[];

    constructor(ticker_internal_id: number, historicalJson: DividendsEODHDAO[]) {
        (this.ticker_internal_id = ticker_internal_id), (this.historicalJson = historicalJson);

        this.dividends = [];
    }

    async getSavedDividends(): Promise<void> {
        try {
            this.dividends = await HistoricalDividends.findAll({
                where: {
                    ticker_internal_id: this.ticker_internal_id,
                },
                attributes: ["document_date"],
            });
        } catch (error: any) {
            console.log("error when try to get saved dividends", error.message);
            addLogJobExecution(LogLevel.ERROR, "getSavedDividends", "Error when try to get saved dividends", {error: error instanceof Error ? error.message : String(error)}, this.ticker_internal_id);
        }
    }

    async getDividends() {
        const dividends_date_saved = [];

        for (let i = 0; i < this.dividends.length; i++) {
            if (this.dividends[i]?.document_date) {
                dividends_date_saved.push(this.dividends[i]?.document_date);
            }
        }

        const dividends_api = [];

        for (let i = 0; i < this.historicalJson.length; i++) {
            if (this.historicalJson[i]?.document_date) {
                dividends_api.push(this.historicalJson[i]?.document_date);
            }
        }

        const diference_dividends = _.difference(dividends_api, dividends_date_saved);

        const toSave: DividendsEODHDAO[] = [];

        for (let i = 0; i < this.historicalJson.length; i++) {
            if (diference_dividends.includes(this.historicalJson[i].document_date)) {
                toSave.push(this.historicalJson[i]);
            }
        }
        addLogJobExecution(
            LogLevel.INFO,
            "getDividends",
            "Dividends to save",
            {toSaveLength: toSave.length, dividends_apiLength: dividends_api.length, dividends_date_savedLength: dividends_date_saved.length, diference_dividendsLength: diference_dividends.length},
            this.ticker_internal_id,
        );

        return toSave;
    }

    async saveDividends(dividends: DividendsEODHDAO[]): Promise<void> {
        if (dividends.length === 0) return;
        await HistoricalDividends.bulkCreate(dividends);
    }
}
