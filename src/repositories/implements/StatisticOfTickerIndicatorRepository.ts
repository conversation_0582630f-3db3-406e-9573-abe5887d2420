import moment from "moment";
import {Op} from "sequelize";
import {BalanceSheet} from "../../entities/consolidated_data/BalanceSheet";
import {CashFlow} from "../../entities/consolidated_data/CashFlow";
import {Document_type_year_or_quarter} from "../../entities/consolidated_data/DocumentTypeYearorQuarterly";
import {HistoricalDividends} from "../../entities/consolidated_data/HistoricalDividends";
import {IncomeStatement} from "../../entities/consolidated_data/IncomeStatement";
import {iStatisticsOfTicker} from "../../entities/consolidated_data/iStatisticsOfTicker";

export class StatisticOfTickerIndicatorRepository {
    ticker: iStatisticsOfTicker;
    balance_sheet: BalanceSheet | null;
    all_balance_sheet: BalanceSheet[] | [];
    cash_flow: CashFlow | null;
    income_statement: IncomeStatement | null;
    all_income_statement: IncomeStatement[] | [];
    income_statement_last_year: IncomeStatement[] | [];
    enterprise_value: number;
    dividends: HistoricalDividends[];
    sum_dividends: number;
    total_revenue: number;
    dividends_count: number;

    constructor(ticker: iStatisticsOfTicker) {
        this.ticker = ticker;
    }

    async get_last_balance_sheet_yearly(balance_sheet: BalanceSheet[]) {
        this.balance_sheet = balance_sheet[0] || {};
        this.all_balance_sheet = balance_sheet;
    }

    async get_last_cash_flow_yearly() {
        this.cash_flow = await CashFlow.findOne({
            where: {
                ticker_internal_id: this.ticker.ticker_internal_id,
                document_type_year_or_quarter: Document_type_year_or_quarter.y,
                document_current: 1,
            },
        });
    }

    async get_last_income_statement_yearly(income_statement: IncomeStatement[], income_statement_last_year: IncomeStatement[]) {
        this.income_statement = income_statement[0] || {}; //First item ordered by document_date DESC
        this.all_income_statement = income_statement; //last 4 ordered by document_date
        this.income_statement_last_year = income_statement_last_year;
    }

    async getDividends() {
        const last_12_months = moment().subtract(11, "months");
        const date_formated = last_12_months.format("YYYY-MM-DD");
        const startedDate = `${date_formated.substring(0, date_formated.length - 2)}01`;

        const endDate = moment().format("YYYY-MM-DD");

        //console.log("dividends from ", startedDate);
        //console.log("dividends to ", endDate);

        const dividends = await HistoricalDividends.findAll({
            where: {
                ticker_internal_id: this.ticker.ticker_internal_id,
                declaration_date: {
                    [Op.gte]: startedDate,
                },
            },
            attributes: ["unadjusted_value", "value"],
        });

        this.dividends = dividends;

        let sum = 0;

        this.dividends_count = dividends.length || 0;

        for (let i = 0; i < dividends.length; i++) {
            const value = dividends[i].value || dividends[i].unadjusted_value;

            if (value) {
                sum = sum + (value || 0);
            }
        }

        this.sum_dividends = sum;
    }

    async createDividendYield() {
        const current_price = this.ticker.price;

        let dividend_yield = 0;

        if (current_price !== 0 && this.dividends_count > 0 && this.sum_dividends > 0) {
            const dividends_avg = this.sum_dividends / this.dividends_count;

            dividend_yield = (this.sum_dividends / current_price) * 100;
        }

        if (dividend_yield !== 0) {
            this.ticker.dividend_yield = dividend_yield;
        }

        //console.log("sum_dividends", this.sum_dividends);
        //console.log("dividends_count", this.dividends_count);
        //console.log("dividend_yield", dividend_yield);
    }

    async createEpsLastDate() {
        let sum_eps_last_date = 0;

        for (let i = 0; i < this.income_statement_last_year.length; i++) {
            sum_eps_last_date = sum_eps_last_date + this.income_statement_last_year[i].eps_diluted_current;
        }

        this.ticker.eps_last_year = sum_eps_last_date;
    }

    async createPe() {
        const current_price = this.ticker.price || 0;
        const eps = this.ticker.eps_current || 0;

        if (eps !== 0) {
            this.ticker.pe = current_price / eps;
            //console.log("PE", this.ticker.pe);
        }
    }

    async createEpsDilutedCurrent() {
        let sum_eps_current = 0;

        for (let i = 0; i < this.all_income_statement.length; i++) {
            sum_eps_current = sum_eps_current + this.all_income_statement[i].eps_diluted_current;
        }

        this.ticker.eps_current = sum_eps_current;
    }

    async createPegRatio() {
        const pe = this.ticker.pe || 0;
        const eps_current = this.ticker.eps_current || 0;
        const eps_last_year = this.ticker.eps_last_year || 0;

        if (eps_last_year !== 0 && eps_current !== 0 && eps_last_year !== 0) {
            let denominator = (eps_current / eps_last_year - 1) * 100;
            if (denominator != 0) {
                this.ticker.peg_ratio = pe / denominator;
                //console.log("peg_ratio", this.ticker.peg_ratio);
            }
        }
    }

    async createBvps() {
        const common_stock_shares_outstanding = this.balance_sheet?.common_stock_shares_outstanding || 0;
        const total_stockholder_equity = this.balance_sheet?.total_stockholder_equity || 0;

        if (common_stock_shares_outstanding !== 0 && total_stockholder_equity !== 0) {
            this.ticker.bvps = total_stockholder_equity / common_stock_shares_outstanding;
            //console.log("bvps", this.ticker.bvps);
        }
    }

    async createPriceToBook() {
        const current_price = this.ticker.price || 0;
        const bvps = this.ticker.bvps || 0;

        if (current_price !== 0 && bvps !== 0) {
            this.ticker.price_to_book = current_price / bvps;
            //console.log("price_to_book", this.ticker.price_to_book);
        }
    }

    async createEbitda() {
        let sum_ebitda = 0;
        for (let i = 0; i < this.all_income_statement.length; i++) {
            sum_ebitda = sum_ebitda + this.all_income_statement[i].ebitda;
        }

        this.ticker.ebitda = sum_ebitda;
    }

    async createEbitdaMargin() {
        const ebitda = this.ticker.ebitda || 0;
        const total_revenue = this.total_revenue || 0;

        if (ebitda !== 0 && total_revenue != 0) {
            this.ticker.ebitda_margin = (ebitda / total_revenue) * 100;
            //console.log("ebitda_margin", this.ticker.ebitda_margin);
        }
    }

    async createEbit() {
        let sum_ebit = 0;

        for (let i = 0; i < this.all_income_statement.length; i++) {
            sum_ebit = sum_ebit + this.all_income_statement[i].ebit;
        }

        this.ticker.ebit = sum_ebit;
    }

    async createOperationMargin() {
        const ebit = this.ticker.ebit || 0;
        const total_revenue = this?.total_revenue || 0;

        if (ebit !== 0 && total_revenue !== 0) {
            this.ticker.operation_margin = (ebit / total_revenue) * 100;
            //console.log("operation_margin", this.ticker.operation_margin);
        }
    }

    async createNetMargin() {
        const net_income = this.income_statement?.net_income || 0;
        const total_revenue = this?.all_income_statement[0]?.total_revenue || 0;

        if (net_income !== 0 && total_revenue !== 0) {
            this.ticker.net_margin = (net_income / total_revenue) * 100;
            //console.log("net_margin", this.ticker.net_margin);
        }
    }

    async createGrossMargin() {
        const total_revenue = this?.total_revenue || 0;
        const gross_profit = this.income_statement?.gross_profit || 0;

        if (total_revenue !== 0 && gross_profit !== 0) {
            this.ticker.gross_margin = (gross_profit / total_revenue) * 100;
            //console.log("gross_margin", this.ticker.gross_margin);
        }
    }

    async createPriceWorkingCapitalShare() {
        const current_price = this.ticker.price || 0;
        const common_stock_shares_outstanding = this.balance_sheet?.common_stock_shares_outstanding || 0;
        const total_current_assets = this.balance_sheet?.total_current_assets || 0;
        const total_current_liabilities = this.balance_sheet?.total_current_liabilities || 0;
        if (common_stock_shares_outstanding !== 0 && current_price !== 0 && total_current_assets !== 0 && total_current_liabilities !== 0) {
            const denominator = (total_current_assets - total_current_liabilities) / common_stock_shares_outstanding;
            if (denominator !== 0) {
                this.ticker.price_working_capital_share = current_price / denominator;
                //console.log("price_working_capital_share", this.ticker.price_working_capital_share);
            }
        }
    }

    async createEbitRatio() {
        const current_price = this.ticker.price || 0;
        const common_stock_shares_outstanding = this.balance_sheet?.common_stock_shares_outstanding || 0;
        const ebit = this.ticker.ebit || 0;

        //console.log("current_price", current_price);
        //console.log("common_stock_shares_outstanding", common_stock_shares_outstanding);
        //console.log("ebit", ebit);

        if (current_price !== 0 && ebit !== 0 && common_stock_shares_outstanding !== 0) {
            this.ticker.ebit_ratio = current_price / (ebit / common_stock_shares_outstanding);
            //console.log("ebit_ratio", this.ticker.ebit_ratio);
        }
    }

    async createNetDebt() {
        if (this.balance_sheet?.net_debt && this.balance_sheet?.net_debt !== 0) {
            this.ticker.net_debt = this.balance_sheet?.net_debt || 0;
            //console.log("net_debt", this.ticker.net_debt);
        }
    }

    async createNetDebtEbit() {
        const net_debt = this.ticker.net_debt || 0;
        const ebit = this.ticker.ebit || 0;

        if (ebit !== 0) {
            this.ticker.net_debt_ebit = net_debt / ebit;
            //console.log("net_debt_ebit", this.ticker.net_debt_ebit);
        }
    }

    async createNetDebtEbitda() {
        const net_debt = this.ticker.net_debt || 0;
        const ebitda = this.ticker.ebitda || 0;

        if (ebitda !== 0) {
            this.ticker.net_debt_ebitda = net_debt / ebitda;
            //console.log("net_debt_ebitda", this.ticker.net_debt_ebitda);
        }
    }

    async createMarketCapitalization() {
        //common_stock_shares_outstanding = 1868101000
        //price = 165.89
        //309899274890
        //306749853240000

        const current_price = this.ticker.price || 0;
        const common_stock_shares_outstanding = this.balance_sheet?.common_stock_shares_outstanding || 0;

        const market_capitalization = current_price * common_stock_shares_outstanding;
        // console.log("price", current_price);
        //console.log("common_stock_shares_outstanding", common_stock_shares_outstanding);

        this.ticker.market_capitalization = market_capitalization;
        //console.log("market_capitalization", this.ticker.market_capitalization);
    }

    async createEV() {
        const market_capitalization = this.ticker.market_capitalization || 0;
        const net_debt = this.ticker.net_debt || 0;

        if (net_debt !== 0 && market_capitalization !== 0) {
            this.enterprise_value = market_capitalization - net_debt;
            //console.log("enterprise_value", this.enterprise_value);
        }
    }

    async createEvEbit() {
        const ebit = this.ticker.ebit || 0;
        const enterprise_value = this.enterprise_value || 0;

        if (ebit !== 0 && enterprise_value !== 0) {
            this.ticker.ev_ebit = enterprise_value / ebit;
            //console.log("ev_ebit", this.ticker.ev_ebit);
        }
    }

    async createNetDebtShareholdersEquity() {
        const net_debt = this.ticker.net_debt || 0;
        const common_stock_shares_outstanding = this.balance_sheet?.common_stock_shares_outstanding || 0;

        if (net_debt !== 0 && common_stock_shares_outstanding !== 0) {
            this.ticker.net_debt_shareholders_equity = net_debt / common_stock_shares_outstanding;
            //console.log("net_debt_shareholders_equity", this.ticker.net_debt_shareholders_equity);
        }
    }

    async createPriceSalesRatio() {
        const market_capitalization = this.ticker.market_capitalization || 0;
        const total_revenue = this?.total_revenue || 0;

        if (market_capitalization !== 0 && total_revenue !== 0) {
            this.ticker.price_sales_ratio = market_capitalization / total_revenue;
            //console.log("price_sales_ratio", this.ticker.price_sales_ratio);
        }
    }

    async createRoeRoaRoic() {
        let sum_roe = 0;
        let sum_roa = 0;
        let sum_roic = 0;

        for (let i = 0; i < 4; i++) {
            const total_stockholder_equity = this.all_balance_sheet[i]?.total_stockholder_equity || 0;
            const total_current_assets = this.all_balance_sheet[i]?.total_assets || 0;
            const short_term_debt = this.all_balance_sheet[i]?.short_term_debt || 0;
            const long_term_debt = this.all_balance_sheet[i]?.long_term_debt || 0;

            const net_income = this.all_income_statement[i]?.net_income;
            const income_tax_expense = this.all_income_statement[i]?.income_tax_expense || this.all_income_statement[i]?.tax_provision || 0;
            const income_before_tax = this.all_income_statement[i]?.income_before_tax || 0;
            const operating_income = this.all_income_statement[i]?.operating_income || 0;

            if (total_stockholder_equity !== 0) {
                sum_roe += net_income / total_stockholder_equity;
            }

            if (total_current_assets !== 0) {
                sum_roa += net_income / total_current_assets;
            }

            const denominator = short_term_debt + long_term_debt + total_stockholder_equity;

            if (denominator !== 0 && income_before_tax !== 0) {
                const tax_rate = income_tax_expense / income_before_tax;
                const nopat = operating_income * (1 - tax_rate);
                sum_roic += nopat / denominator;
            }
        }

        this.ticker.roe = sum_roe * 100;
        this.ticker.roa = sum_roa * 100;
        this.ticker.roic = sum_roic * 100;

        //console.log("roe", this.ticker.roe);
        //console.log("roa", this.ticker.roa);
        //console.log("roic", this.ticker.roic);
    }

    async createRoe() {
        // const total_stockholder_equity = this.balance_sheet?.total_stockholder_equity || 0
        // const net_income = this.income_statement?.net_income || 0
        // if (total_stockholder_equity !== 0 && net_income != 0) {
        //     this.ticker.roe = (net_income/total_stockholder_equity) * 100;
        //     console.log('roe',this.ticker.roe)
        // }
    }

    async createRoa() {
        // const net_income = this.income_statement?.net_income || 0
        // const total_current_assets = this.balance_sheet?.total_current_assets || 0
        // if (total_current_assets !== 0 && net_income !== 0) {
        //     this.ticker.roa = ((net_income)/(total_current_assets)) * 100
        //     console.log('roa',this.ticker.roa)
        // }
    }

    async createRoic() {
        const net_income = this.income_statement?.net_income || 0;
        const diluted_shares_outstanding = this.balance_sheet?.common_stock_shares_outstanding || 0;
        const short_term_debt = this.balance_sheet?.short_term_debt || 0;
        const long_term_debt = this.balance_sheet?.long_term_debt || 0;
        const total_stockholder_equity = this.balance_sheet?.total_stockholder_equity || 0;

        const denominator = short_term_debt + long_term_debt + total_stockholder_equity;

        if (denominator !== 0) {
            this.ticker.roic = ((net_income - this.sum_dividends * diluted_shares_outstanding) / denominator) * 100;
            //console.log("roic", this.ticker.roic);
        }
    }

    async createCurrentRatio() {
        const total_current_assets = this.balance_sheet?.total_current_assets || 0;
        const total_current_liabilities = this.balance_sheet?.total_current_liabilities || 0;

        if (total_current_assets !== 0 && total_current_liabilities !== 0) {
            this.ticker.current_ratio = total_current_assets / total_current_liabilities;
            //console.log("current_ratio", this.ticker.current_ratio);
        }
    }

    async createShareholderEquityRatio() {
        const total_stockholder_equity = this.balance_sheet?.total_stockholder_equity || 0;
        const total_current_assets = this.balance_sheet?.total_current_assets || 0;

        if (total_current_assets != 0) {
            this.ticker.shareholder_equity_ratio = total_stockholder_equity / total_current_assets;
            //console.log("current_ratio", this.ticker.current_ratio);
        }
    }

    async createTotalDebtToTotalAssetsRatio() {
        const total_current_assets = this.balance_sheet?.total_current_assets || 0;
        const total_liab = this.balance_sheet?.total_liab || 0;

        if (total_current_assets !== 0 && total_liab !== 0) {
            this.ticker.total_debt_to_total_assets_ratio = total_current_assets / total_liab;
            //console.log("total_debt_to_total_assets_ratio", this.ticker.total_debt_to_total_assets_ratio);
        }
    }

    async createAssetTurnover() {
        const total_current_assets = this.balance_sheet?.total_current_assets || 0;
        const total_revenue = this?.total_revenue || 0;

        if (total_current_assets !== 0 && total_revenue !== 0) {
            this.ticker.asset_turnover = total_revenue / total_current_assets;
            //console.log("asset_turnover", this.ticker.asset_turnover);
        }
    }

    async createPayoutRatio() {
        const sum_dividends = this.sum_dividends;
        const eps = this.ticker.eps_current || 0;

        if (eps !== 0) {
            this.ticker.payout_ratio = sum_dividends / eps;
        }

        //console.log("eps", this.ticker.eps_current);
        //console.log("sum_dividends", this.sum_dividends);
        //console.log("payout_ratio", this.ticker.payout_ratio);
    }

    async createTotalRevenue() {
        let sum_total_revenue = 0;
        for (let i = 0; i < this.all_income_statement.length; i++) {
            sum_total_revenue += this.all_income_statement[i].total_revenue;
        }

        console.log("Total revenue", sum_total_revenue);

        this.total_revenue = sum_total_revenue;
    }
}
