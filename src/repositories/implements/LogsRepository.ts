import {Logs} from "../../entities/consolidated_data/Logs";

export class LogsRepository {
    async createOrUpdateLog(_log: Partial<Logs>) {
        try {
            _log.latest_update = new Date();
            const log = await Logs.findOne({
                where: {
                    ticker_internal_id: _log.ticker_internal_id,
                },
            });

            if (log) {
                log.setAttributes(_log);
                await log.save();
            } else {
                const lastLog = await Logs.findOne({
                    order: [["id", "DESC"]],
                });
                let nextId = lastLog && lastLog.id ? lastLog.id + 1 : 1;
                _log.id = nextId;
                await Logs.create(_log);
            }
        } catch (error: any) {
            console.log(error.message);
        }
    }
}
