import {CashFlow} from "../../entities/consolidated_data/CashFlow";
import {CashFLowDAO} from "./CashFlowDAO";
import {Document_type_year_or_quarter} from "../../entities/consolidated_data/DocumentTypeYearorQuarterly";
import {snakeCase, difference} from "lodash";
import {iCashFlowDAO} from "../iCashFlowDAO";

export class CashFlowRepository {
    saved_yearly: string[];
    saved_quarterly: string[];
    cashFlowYearly: CashFLowDAO[];
    cashFlowQuarterly: CashFLowDAO[];
    ticker_internal_id: number;
    files_dates_yearly: string[];
    files_dates_quarterly: string[];

    constructor(ticker_internal_id: number) {
        this.ticker_internal_id = ticker_internal_id;
    }

    async getSavedYearly(): Promise<void> {
        const saved_yearly = await CashFlow.findAll({
            where: {
                ticker_internal_id: this.ticker_internal_id,
                document_type_year_or_quarter: Document_type_year_or_quarter.y,
            },
            attributes: ["document_date"],
        });

        const data: string[] = [];

        for (let i = 0; i < saved_yearly.length; i++) {
            data.push(saved_yearly[i].document_date);
        }

        this.saved_yearly = data;
    }

    async getSavedQuarterly(): Promise<void> {
        const saved_quarterly = await CashFlow.findAll({
            where: {
                ticker_internal_id: this.ticker_internal_id,
                document_type_year_or_quarter: Document_type_year_or_quarter.q,
            },
            attributes: ["document_date"],
        });

        const data: string[] = [];

        for (let i = 0; i < saved_quarterly.length; i++) {
            data.push(saved_quarterly[i].document_date);
        }

        this.saved_quarterly = data;
    }

    async getYearly(cashFlowYearly: any) {
        try {
            const data: CashFLowDAO[] = [];

            const keys = Object.keys(cashFlowYearly);

            this.files_dates_yearly = keys;

            for (let i = 0; i < keys.length; i++) {
                const current_key = keys[i];

                const cashFlow = cashFlowYearly[current_key];

                const snakeCasedObject = Object.keys(cashFlow).reduce(
                    (result, key) => ({
                        ...result,
                        [snakeCase(key)]: cashFlow[key],
                    }),
                    {},
                );

                const params: iCashFlowDAO = {
                    ...snakeCasedObject,
                    ticker_internal_id: this.ticker_internal_id,
                    document_type_year_or_quarter: Document_type_year_or_quarter.y,
                    date: current_key,
                };

                const cashFlowData = new CashFLowDAO(params);

                data.push(cashFlowData);
            }

            this.cashFlowYearly = data;
        } catch (error: any) {
            throw Error("Error when try to get CashFlow Yearly " + error.message);
        }
    }

    async getQuarterly(cashFlowQuarterly: any) {
        try {
            const data: CashFLowDAO[] = [];

            const keys = Object.keys(cashFlowQuarterly);

            this.files_dates_quarterly = keys;

            for (let i = 0; i < keys.length; i++) {
                const current_key = keys[i];

                const cashFlow = cashFlowQuarterly[current_key];

                const snakeCasedObject = Object.keys(cashFlow).reduce(
                    (result, key) => ({
                        ...result,
                        [snakeCase(key)]: cashFlow[key],
                    }),
                    {},
                );

                const params: iCashFlowDAO = {
                    ...snakeCasedObject,
                    ticker_internal_id: this.ticker_internal_id,
                    document_type_year_or_quarter: Document_type_year_or_quarter.q,
                    date: current_key,
                };

                const cashFlowData = new CashFLowDAO(params);

                data.push(cashFlowData);
            }

            this.cashFlowQuarterly = data;
        } catch (error: any) {
            throw Error("Error when try to get CashFlow Quarterly " + error.message);
        }
    }

    async saveYearly() {
        try {
            let toSave: CashFLowDAO[] = [];

            if (this.saved_yearly.length === 0 && this.cashFlowYearly.length > 0) {
                toSave = this.cashFlowYearly;
                toSave[0].document_current = 1;
            } else {
                const delta = difference(this.files_dates_yearly, this.saved_yearly);
                const biggest_date = this.files_dates_yearly[0];

                if (delta.length > 0 && this.cashFlowYearly.length > 0) {
                    for (let i = 0; i < this.cashFlowYearly.length; i++) {
                        if (delta.includes(this.cashFlowYearly[i].document_date)) {
                            if (this.cashFlowYearly[i].document_date === biggest_date) {
                                this.cashFlowYearly[i].document_current = 1;
                            }
                            toSave.push(this.cashFlowYearly[i]);
                        }
                    }
                }

                const cash = await CashFlow.findOne({
                    where: {
                        document_current: 1,
                        ticker_internal_id: this.ticker_internal_id,
                        document_type_year_or_quarter: Document_type_year_or_quarter.y,
                    },
                });

                if (cash && cash?.document_date < biggest_date) {
                    await cash.update({document_current: 0});
                    await cash.save();
                }
            }

            if (toSave.length > 0) {
                await CashFlow.bulkCreate(toSave);
                console.log("CashFlow yearly saved");
            }
        } catch (error: any) {
            throw "Error when save CashFlow yearly " + error.message;
        }
    }

    async saveQuarterly() {
        try {
            let toSave: CashFLowDAO[] = [];

            if (this.saved_quarterly.length === 0 && this.cashFlowQuarterly.length > 0) {
                toSave = this.cashFlowQuarterly;
                toSave[0].document_current = 1;
            } else {
                const delta = difference(this.files_dates_quarterly, this.saved_quarterly);

                if (delta.length > 0 && this.cashFlowQuarterly.length > 0) {
                    const biggest_date = this.files_dates_quarterly[0];

                    for (let i = 0; i < this.cashFlowQuarterly.length; i++) {
                        if (delta.includes(this.cashFlowQuarterly[i].document_date)) {
                            if (this.cashFlowQuarterly[i].document_date === biggest_date) {
                                this.cashFlowQuarterly[i].document_current = 1;
                            }
                            toSave.push(this.cashFlowQuarterly[i]);
                        }
                    }

                    const cash = await CashFlow.findOne({
                        where: {
                            document_current: 1,
                            ticker_internal_id: this.ticker_internal_id,
                            document_type_year_or_quarter: Document_type_year_or_quarter.q,
                        },
                    });

                    if (cash && cash?.document_date < biggest_date) {
                        await cash.update({document_current: 0});
                        await cash.save();
                    }
                }
            }

            if (toSave.length > 0) {
                await CashFlow.bulkCreate(toSave);
                console.log("CashFlow quarterly saved");
            }
        } catch (error: any) {
            throw "Error when save CashFlow quarterly " + error.message;
        }
    }
}
