import {S3Client, PutObjectCommand, ListO<PERSON>sCommand, GetObjectCommand, CopyObjectCommand, DeleteObjectCommand, ListObjectsCommandInput} from "@aws-sdk/client-s3";
import {iFundamentalDataRepository} from "../iFundamentaDataRepository";
import {BUCKET_NAME, s3} from "../../factory";

const {SEARCH_FILES_LIMIT} = process.env;

const limit = parseInt(SEARCH_FILES_LIMIT || "50");

export class FundamentalDataRepository implements iFundamentalDataRepository {
    private s3Client: S3Client;

    constructor() {
        this.s3Client = s3;
    }

    async create(contentJson: string, primaryTicker: string): Promise<string> {
        const fileName = `fundamentals_${primaryTicker}_eod.json`;

        await this.s3Client.send(
            new PutObjectCommand({
                Bucket: BUCKET_NAME,
                Key: fileName,
                Body: contentJson,
                ContentType: "application/json; charset=utf-8",
            }),
        );

        console.log(primaryTicker, "was saved into s3");

        return fileName;
    }

    async index(): Promise<any[]> {
        const params: ListObjectsCommandInput = {
            Bucket: BUCKET_NAME,
            MaxKeys: limit,
        };

        const files = await this.s3Client.send(new ListObjectsCommand(params));

        return files.Contents || [];
    }

    async get(key: string): Promise<any> {
        const data = await this.s3Client.send(
            new GetObjectCommand({
                Bucket: BUCKET_NAME,
                Key: key,
            }),
        );

        const response = await data?.Body?.transformToString();

        const json = JSON.parse(response || "{}");

        return json;
    }

    async move(key: string, folder?: string) {
        try {
            await this.s3Client.send(
                new CopyObjectCommand({
                    CopySource: `${BUCKET_NAME}/${key}`,
                    Bucket: BUCKET_NAME,
                    Key: `${folder}/${key}`,
                }),
            );

            await this.s3Client.send(
                new DeleteObjectCommand({
                    Bucket: BUCKET_NAME,
                    Key: key,
                }),
            );

            console.log(`file ${key} moved to ${folder}`);
        } catch (error: any) {
            throw Error(`Error when try to move file to ${folder} ${error.message}`);
        }
    }
}
