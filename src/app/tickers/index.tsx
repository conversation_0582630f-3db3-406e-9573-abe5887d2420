import { useEffect, useState } from 'react';
import Head from 'next/head';
import axios from 'axios';

interface Ticker {
  id: number;
  symbol_code: string;
  primary_ticker_eodhd: string;
  country_code: string;
  exchange_code: string;
  currency_code: string;
  type: string;
  is_enable: number;
  fundamental_data_last_updated: string | null;
  log_eodhd?: string;
}

export default function TickersPage() {
  const [tickers, setTickers] = useState<Ticker[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);
  
  const fetchTickers = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/api/tickers');
      setTickers(response.data);
      setError(null);
    } catch (err) {
      setError("Failed to fetch tickers");
      console.error(err);
    } finally {
      setLoading(false);
    }
  };
  
  const refreshTicker = async (tickerId: number) => {
    try {
      setRefreshing(true);
      await axios.post(`/api/tickers/${tickerId}/refresh`);
      await fetchTickers(); // Refresh the list
    } catch (err) {
      setError("Failed to refresh ticker");
      console.error(err);
    } finally {
      setRefreshing(false);
    }
  };
  
  useEffect(() => {
    fetchTickers();
  }, []);
  
  return (
    <>
      <Head>
        <title>Ticker Management - Tiba Invest API</title>
      </Head>
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold mb-6">Ticker Management</h1>
        
        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        )}
        
        <div className="flex justify-between mb-4">
          <button 
            onClick={fetchTickers}
            className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            disabled={loading}
          >
            Refresh List
          </button>
          
          <button 
            onClick={async () => {
              try {
                setRefreshing(true);
                await axios.post('/api/tickers/refresh-outdated');
                await fetchTickers();
              } catch (err) {
                setError("Failed to refresh outdated tickers");
              } finally {
                setRefreshing(false);
              }
            }}
            className="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded"
            disabled={refreshing}
          >
            {refreshing ? "Processing..." : "Refresh Outdated Tickers"}
          </button>
        </div>
        
        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full bg-white border border-gray-200">
              <thead>
                <tr>
                  <th className="px-4 py-2 border">ID</th>
                  <th className="px-4 py-2 border">Symbol</th>
                  <th className="px-4 py-2 border">Primary Ticker</th>
                  <th className="px-4 py-2 border">Country</th>
                  <th className="px-4 py-2 border">Exchange</th>
                  <th className="px-4 py-2 border">Status</th>
                  <th className="px-4 py-2 border">Last Updated</th>
                  <th className="px-4 py-2 border">Actions</th>
                </tr>
              </thead>
              <tbody>
                {tickers.map((ticker) => (
                  <tr key={ticker.id}>
                    <td className="px-4 py-2 border">{ticker.id}</td>
                    <td className="px-4 py-2 border">{ticker.symbol_code}</td>
                    <td className="px-4 py-2 border">{ticker.primary_ticker_eodhd}</td>
                    <td className="px-4 py-2 border">{ticker.country_code}</td>
                    <td className="px-4 py-2 border">{ticker.exchange_code}</td>
                    <td className="px-4 py-2 border">
                      <span className={`px-2 py-1 rounded ${ticker.is_enable ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                        {ticker.is_enable ? 'Enabled' : 'Disabled'}
                      </span>
                    </td>
                    <td className="px-4 py-2 border">
                      {ticker.fundamental_data_last_updated 
                        ? new Date(ticker.fundamental_data_last_updated).toLocaleDateString() 
                        : 'Never'}
                    </td>
                    <td className="px-4 py-2 border">
                      <button
                        onClick={() => refreshTicker(ticker.id)}
                        className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-2 rounded text-sm"
                        disabled={refreshing}
                      >
                        Refresh
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </>
  );
}