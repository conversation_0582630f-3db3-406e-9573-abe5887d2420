.container {
    flex: 1;
    padding: 2rem;
    padding-top: 1.5rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.header {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.title {
    font-size: 1.875rem;
    font-weight: 700;
    letter-spacing: -0.025em;
}

.statusGrid {
    display: grid;
    gap: 1rem;
    grid-template-columns: repeat(1, minmax(0, 1fr));
}

@media (min-width: 768px) {
    .statusGrid {
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }
}

@media (min-width: 1024px) {
    .statusGrid {
        grid-template-columns: repeat(4, minmax(0, 1fr));
    }
}

.chartsGrid {
    display: grid;
    gap: 1rem;
    grid-template-columns: repeat(1, minmax(0, 1fr));
}

@media (min-width: 768px) {
    .chartsGrid {
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }
}

@media (min-width: 1024px) {
    .chartsGrid {
        grid-template-columns: 4fr 3fr;
    }
}

.overviewCard,
.recentCard,
.apiCreditsChartCard {
    background-color: var(--card);
    border-radius: 0.5rem;
    border: 1px solid var(--border);
    overflow: hidden;
}

.cardHeader {
    padding: 1.5rem 1.5rem 0.5rem 1.5rem;
}

.cardTitle {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.cardDescription {
    color: var(--muted-foreground);
    font-size: 0.875rem;
}

.cardContent {
    padding: 1.5rem;
}

.apiCreditsSection {
    margin-top: 0.5rem;
}

.jobProgressSection {
    margin-top: 0.5rem;
}
