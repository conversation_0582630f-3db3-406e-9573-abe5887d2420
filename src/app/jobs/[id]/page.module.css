.container {
    flex: 1;
    padding: 2rem;
    padding-top: 1.5rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.backLink {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.backIcon {
    height: 1rem;
    width: 1rem;
}

.header {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.title {
    font-size: 1.875rem;
    font-weight: 700;
    letter-spacing: -0.025em;
}

.description {
    color: var(--muted-foreground);
}

.runButton {
    display: inline-flex;
    align-items: center;
}

.refreshIcon {
    height: 1rem;
    width: 1rem;
    margin-right: 0.5rem;
}

.playIcon {
    height: 1rem;
    width: 1rem;
    margin-right: 0.5rem;
}

.statusGrid {
    display: grid;
    gap: 1rem;
    grid-template-columns: repeat(1, minmax(0, 1fr));
}

@media (min-width: 768px) {
    .statusGrid {
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }
}

@media (min-width: 1024px) {
    .statusGrid {
        grid-template-columns: repeat(4, minmax(0, 1fr));
    }
}

.statusCard {
    background-color: var(--card);
    border-radius: 0.5rem;
    border: 1px solid var(--border);
    overflow: hidden;
}

.statusCardHeader {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 1rem 0.5rem 1rem;
}

.statusCardTitle {
    font-size: 0.875rem;
    font-weight: 500;
}

.statusCardContent {
    padding: 0 1rem 1rem 1rem;
}

.statusCardValue {
    font-size: 1.5rem;
    font-weight: 700;
}

.statusCardDescription {
    font-size: 0.75rem;
    color: var(--muted-foreground);
}

.badge {
    display: inline-flex;
    align-items: center;
    border-radius: 9999px;
    padding: 0.25rem 0.75rem;
    font-size: 0.75rem;
    font-weight: 500;
}

.running {
    background-color: var(--secondary);
    color: var(--secondary-foreground);
}

.idle {
    background-color: transparent;
    border: 1px solid var(--border);
    color: var(--foreground);
}

.tabs {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.tabsList {
    display: flex;
    background-color: var(--muted);
    border-radius: 0.5rem;
    padding: 0.25rem;
}

.tabsTrigger {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    border-radius: 0.25rem;
    cursor: pointer;
    transition: background-color 0.2s;
}

.tabsTrigger[data-state="active"] {
    background-color: var(--background);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.tabsContent {
    display: none;
}

.tabsContent[data-state="active"] {
    display: block;
}

.card {
    background-color: var(--card);
    border-radius: 0.5rem;
    border: 1px solid var(--border);
    overflow: hidden;
}

.cardHeader {
    padding: 1.5rem 1.5rem 0.5rem 1.5rem;
}

.cardTitle {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.cardDescription {
    color: var(--muted-foreground);
    font-size: 0.875rem;
}

.cardContent {
    padding: 1.5rem;
}

.codeBlock {
    background-color: var(--muted);
    padding: 1rem;
    border-radius: 0.5rem;
    overflow: auto;
    font-family: monospace;
    font-size: 0.875rem;
}

/* Add these styles for the job data container and JSON editor */

.jobDataContainer {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    width: 100%;
}

.jsonEditor {
    width: 100%;
    font-family: monospace;
    padding: 1rem;
    border-radius: 0.5rem;
    border: 1px solid var(--border);
    background-color: var(--muted);
    color: var(--foreground);
    resize: vertical;
    min-height: 200px;
}

.jsonEditorError {
    border-color: var(--destructive);
}

.jsonError {
    color: var(--destructive);
    font-size: 0.875rem;
}

.jobDataActions {
    display: flex;
    justify-content: flex-end;
    margin-top: 1rem;
}

/* Job Settings Form Styles */
.settingsForm {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    width: 100%;
    max-width: 600px;
}

.formGroup {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.formRow {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.formLabel {
    font-weight: 500;
}

.formInput {
    width: 100%;
}

.formHelp {
    font-size: 0.875rem;
    color: var(--muted-foreground);
}

.formActions {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 0.5rem;
    margin-top: 1rem;
}

.successMessage {
    color: var(--success, #10b981);
    font-size: 0.875rem;
    font-weight: 500;
    padding: 0.5rem;
    border-radius: 0.25rem;
    background-color: rgba(16, 185, 129, 0.1);
}

.errorMessage {
    color: var(--destructive, #ef4444);
    font-size: 0.875rem;
    font-weight: 500;
    padding: 0.5rem;
    border-radius: 0.25rem;
    background-color: rgba(239, 68, 68, 0.1);
}

.toggleContainer {
    display: flex;
    align-items: center;
}

.enhancedToggle {
    transform: scale(1.3);
    --switch-thumb-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    border: 2px solid var(--border);
    transition: all 0.2s ease;
}

.enhancedToggle[data-state="checked"] {
    background-color: var(--success);
    border-color: var(--success);
}

.enhancedToggle[data-state="unchecked"] {
    background-color: var(--muted);
}

.enhancedToggle[data-state="checked"] .enhancedToggleThumb,
.enhancedToggle[data-state="unchecked"] .enhancedToggleThumb {
    background-color: white;
}
