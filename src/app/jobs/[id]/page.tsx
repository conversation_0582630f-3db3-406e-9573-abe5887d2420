'use client'

import Link from "next/link"
import { <PERSON><PERSON>ronLeft, Play, RefreshCw } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Switch } from "@/components/ui/switch"
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { JobHistoryTable } from "@/components/job-history-table"
import styles from "./page.module.css"
import axios from "axios"
import { useEffect, useState } from "react"
import { JobDetails } from "@/utils/types/agenda/job"

export default function JobDetailPage({ params }: { params: { id: string } }) {
  const { id: jobId } = params;
  const [jobDetails, setJobDetails] = useState<JobDetails>();
  const [isPolling, setIsPolling] = useState(false);
  const [jobData, setJobData] = useState(JSON.stringify(jobDetails?.data, null, 2))
  const [isValid<PERSON><PERSON>, setIsValidJson] = useState(true)
  const [refreshTrigger, setRefreshTrigger] = useState(0)

  const handleSettingChange = (setting: string, value: string | number | boolean) => {
    setJobDetails((prev: any) => ({
      ...prev,
      [setting]: value,
    }))
  }

  const [isSaving, setIsSaving] = useState(false);
  const [saveError, setSaveError] = useState<string | null>(null);
  const [saveSuccess, setSaveSuccess] = useState(false);

  const saveSettings = async () => {
    if (!jobDetails) return;

    try {
      setIsSaving(true);
      setSaveError(null);
      setSaveSuccess(false);

      // Extract only the settings we want to update
      const settings = {
        repeatInterval: jobDetails.repeatInterval,
        disabled: jobDetails.disabled,
        priority: jobDetails.priority
      };

      console.log("Saving job settings:", settings);

      // Call the API to update the job settings
      const response = await axios.put('/api/jobs/update', {
        id: jobId,
        settings
      });

      console.log("Settings saved successfully:", response.data);

      // Update the job details with the response data
      setJobDetails(response.data.job);

      // Show success message
      setSaveSuccess(true);

      // Refresh the job history
      setRefreshTrigger(prev => prev + 1);

      // Hide success message after 3 seconds
      setTimeout(() => {
        setSaveSuccess(false);
      }, 3000);
    } catch (error: any) {
      console.error("Failed to save job settings:", error);
      setSaveError(error.response?.data?.error || "Failed to save settings");
    } finally {
      setIsSaving(false);
    }
  }

  const handleDataChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setJobData(e.target.value)
    try {
      JSON.parse(e.target.value)
      setIsValidJson(true)
    } catch (error) {
      setIsValidJson(false)
    }
  }

  const handleRunJob = async (runWithCustomData: boolean = false) => {
    if(runWithCustomData && !isValidJson) return
    try {
      if(runWithCustomData) {
      const parsedData = JSON.parse(jobData)
      console.log("Running job with data:", parsedData)

      // Call our API endpoint to run the job with custom data
      const response = await axios.post('/api/jobs/run', {
        id: jobId,
        data: parsedData
      });

      console.log("Job executed:", response.data);
    } else {
       const response = await axios.post('/api/jobs/run', { id: jobId });
       console.log("Job executed:", response.data);
    }
      setIsPolling(true);
      // Wait a moment for the job to start before refreshing details
      setTimeout(() => {
        fetchJobDetails();
        // Also refresh the job history
        setRefreshTrigger(prev => prev + 1);
      }, 1000);
    } catch (error) {
      console.error("Failed to run job:", error)
      setIsPolling(false);
    }
  }

  const fetchJobDetails = async () => {
    try {
      const response = await axios.get(`/api/jobs/details?id=${jobId}`);
      setJobDetails(response.data.job);
      console.log("Job details fetched:", response.data.job);
    } catch (error) {
      console.error('Error fetching job status:', error);
    }finally {
      setIsPolling(false);
    }
  };

  // Fetch job details on mount
  useEffect(() => {
    fetchJobDetails();

    // Clean up polling on unmount
    return () => {
      setIsPolling(false);
    };
  }, [jobId]);

  return (
    <div className={styles.container}>
      <div className={styles.backLink}>
        <Button variant="ghost" size="sm" asChild>
          <Link href="/jobs">
            <ChevronLeft className={styles.backIcon} />
            Back to Jobs
          </Link>
        </Button>
      </div>

      <div className={styles.header}>
        <div>
          <h2 className={styles.title}>{jobDetails?.name}</h2>
        </div>
        <div className={styles.headerButtons}>
          <Button
            className={styles.runButton}
            onClick={() => handleRunJob(false)}
            disabled={isPolling}
          >
            <Play className={styles.playIcon} />
            {isPolling ? 'Running...' : 'Run Now'}
          </Button>
          <Button
            className={styles.refreshButton}
            onClick={() => {
              setIsPolling(true);
              fetchJobDetails();
              // Increment refresh trigger to cause JobHistoryTable to refresh
              setRefreshTrigger(prev => prev + 1);
            }}
            variant="outline"
          >
            <RefreshCw className={styles.refreshIcon} />
            Refresh
          </Button>
        </div>
      </div>

      <div className={styles.statusGrid}>
        <div className={styles.statusCard}>
          <div className={styles.statusCardHeader}>
            <h3 className={styles.statusCardTitle}>Schedule</h3>
          </div>
          <div className={styles.statusCardContent}>
            <div className={styles.statusCardValue}>{jobDetails?.repeatInterval}</div>
            <p className={styles.statusCardDescription}>Cron/human expression or manual</p>
          </div>
        </div>
        <div className={styles.statusCard}>
          <div className={styles.statusCardHeader}>
            <h3 className={styles.statusCardTitle}>Last Run</h3>
          </div>
          <div className={styles.statusCardContent}>
            <div className={styles.statusCardValue}>{jobDetails?.lastRunAt ? new Date(jobDetails.lastRunAt).toLocaleString() : 'Not run yet'}</div>
            <p className={styles.statusCardDescription}>Last time it was executed</p>
          </div>
        </div>
        <div className={styles.statusCard}>
          <div className={styles.statusCardHeader}>
            <h3 className={styles.statusCardTitle}>Next Run</h3>
          </div>
          <div className={styles.statusCardContent}>
            <div className={styles.statusCardValue}>{jobDetails?.nextRunAt ? new Date(jobDetails.nextRunAt).toLocaleString() : 'Not scheduled'}</div>
            <p className={styles.statusCardDescription}>Scheduled</p>
          </div>
        </div>
        <div className={styles.statusCard}>
          <div className={styles.statusCardHeader}>
            <h3 className={styles.statusCardTitle}>Status</h3>
          </div>
          <div className={styles.statusCardContent}>
            <div className={styles.statusCardValue}>
              <span className={`${styles.badge} ${jobDetails?.disabled === false ? styles.running : styles.idle}`}>
                {jobDetails?.disabled ? 'Disabled' : 'Enabled'}
              </span>
            </div>
            <p className={styles.statusCardDescription}>Current state</p>
          </div>
        </div>
      </div>

      <Tabs defaultValue="history" className={styles.tabs}>
        <TabsList className={styles.tabsList}>
          <TabsTrigger value="history" className={styles.tabsTrigger}>
            Execution History
          </TabsTrigger>
          <TabsTrigger value="data" className={styles.tabsTrigger}>
            Job Data
          </TabsTrigger>
          <TabsTrigger value="settings" className={styles.tabsTrigger}>
            Settings
          </TabsTrigger>
        </TabsList>
        <TabsContent value="history" className={styles.tabsContent}>
          <div className={styles.card}>
            <div className={styles.cardHeader}>
              <h3 className={styles.cardTitle}>Execution History</h3>
              <p className={styles.cardDescription}>View all previous executions of this job</p>
            </div>
            <div className={styles.cardContent}>
              <JobHistoryTable jobId={jobId} refreshTrigger={refreshTrigger} />
            </div>
          </div>
        </TabsContent>
        <TabsContent value="data" className={styles.tabsContent}>
          <div className={styles.card}>
            <div className={styles.cardHeader}>
              <h3 className={styles.cardTitle}>Job Data</h3>
              <p className={styles.cardDescription}>Edit the data that will be passed to the job when it runs</p>
            </div>
            <div className={styles.cardContent}>
              <div className={styles.jobDataContainer}>
                <textarea
                  className={`${styles.jsonEditor} ${!isValidJson ? styles.jsonEditorError : ""}`}
                  value={jobData}
                  onChange={handleDataChange}
                  rows={10}
                />
                {!isValidJson && <p className={styles.jsonError}>Invalid JSON format</p>}
                <div className={styles.jobDataActions}>
                  <Button
                    onClick={() => handleRunJob(true)}
                    disabled={!isValidJson || isPolling}
                    className={styles.runButton}
                  >
                    <Play className={styles.playIcon} />
                    {isPolling ? 'Running...' : 'Run Now with This Data'}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </TabsContent>
        <TabsContent value="settings" className={styles.tabsContent}>
          <div className={styles.card}>
            <div className={styles.cardHeader}>
              <h3 className={styles.cardTitle}>Job Settings</h3>
              <p className={styles.cardDescription}>Configure job settings and behavior</p>
            </div>
            <div className={styles.cardContent}>
              <form
                className={styles.settingsForm}
                onSubmit={(e) => {
                  e.preventDefault()
                  saveSettings()
                }}
              >
                <div className={styles.formGroup}>
                  <Label htmlFor="repeatInterval" className={styles.formLabel}>
                    Repeat Interval
                  </Label>
                  <Input
                    id="repeatInterval"
                    value={jobDetails?.repeatInterval || ""}
                    onChange={(e) => handleSettingChange("repeatInterval", e.target.value)}
                    className={styles.formInput}
                    placeholder="e.g. 30 minutes, 1 hour, or cron expression"
                  />
                  <p className={styles.formHelp}>
                    How often the job should repeat. Use a human-readable string like '30 minutes' or '1 hour', or a
                    cron expression like '*/30 * * * *' for more precise scheduling.
                  </p>
                </div>

                <div className={styles.formGroup}>
                  <div className={styles.formRow}>
                    <div>
                      <Label htmlFor="disabled" className={styles.formLabel}>
                        Disabled
                      </Label>
                      <p className={styles.formHelp}>When disabled, the job will not be processed by Agenda.</p>
                    </div>
                    <div className={styles.toggleContainer}>
                      <Switch
                        id="disabled"
                        checked={jobDetails?.disabled}
                        onCheckedChange={(checked) => handleSettingChange("disabled", checked)}
                        className={styles.enhancedToggle}
                        aria-label={`Job is ${jobDetails?.disabled ? "disabled" : "enabled"}`}
                      />
                    </div>
                  </div>
                </div>

                <div className={styles.formGroup}>
                  <Label htmlFor="priority" className={styles.formLabel}>
                    Priority
                  </Label>
                  <Input
                    id="priority"
                    type="number"
                    value={jobDetails?.priority}
                    onChange={(e) => handleSettingChange("priority", Number.parseInt(e.target.value) || 0)}
                    className={styles.formInput}
                    min="0"
                  />
                  <p className={styles.formHelp}>Jobs with higher priority will be processed first (0 is default).</p>
                </div>

                <div className={styles.formActions}>
                  <Button type="submit" disabled={isSaving}>
                    {isSaving ? 'Saving...' : 'Save Settings'}
                  </Button>

                  {saveSuccess && (
                    <div className={styles.successMessage}>
                      Settings saved successfully!
                    </div>
                  )}

                  {saveError && (
                    <div className={styles.errorMessage}>
                      {saveError}
                    </div>
                  )}
                </div>
              </form>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
