.container {
    flex: 1;
    padding: 2rem;
    padding-top: 1.5rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.header {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.title {
    font-size: 1.875rem;
    font-weight: 700;
    letter-spacing: -0.025em;
}

.card {
    background-color: var(--card);
    border-radius: 0.5rem;
    border: 1px solid var(--border);
    overflow: hidden;
}

.cardHeader {
    padding: 1.5rem 1.5rem 0.5rem 1.5rem;
}

.cardTitle {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.cardDescription {
    color: var(--muted-foreground);
    font-size: 0.875rem;
}

.cardContent {
    padding: 1.5rem;
}

.tableContainer {
    overflow-x: auto;
}

.table {
    width: 100%;
    border-collapse: collapse;
}

.tableHeader {
    background-color: var(--muted);
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.tableHead {
    padding: 0.75rem;
    text-align: left;
    color: var(--muted-foreground);
    font-weight: 500;
}

.tableRow {
    border-bottom: 1px solid var(--border);
}

.tableRow:last-child {
    border-bottom: none;
}

.tableCell {
    padding: 0.75rem;
    font-size: 0.875rem;
}

.textRight {
    text-align: right;
}

.jobLink {
    font-weight: 500;
    text-decoration: none;
    color: var(--foreground);
}

.jobLink:hover {
    text-decoration: underline;
}

.badge {
    display: inline-flex;
    align-items: center;
    border-radius: 9999px;
    padding: 0.25rem 0.75rem;
    font-size: 0.75rem;
    font-weight: 500;
}

.running {
    background-color: var(--secondary);
    color: var(--secondary-foreground);
}

.idle {
    background-color: transparent;
    border: 1px solid var(--border);
    color: var(--foreground);
}

.runButton {
    display: inline-flex;
    align-items: center;
    font-size: 0.875rem;
}

.playIcon {
    height: 1rem;
    width: 1rem;
    margin-right: 0.25rem;
}
