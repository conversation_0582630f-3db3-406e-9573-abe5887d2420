{"version": "0.2.0", "configurations": [{"type": "node", "request": "launch", "name": "Debug Next.js", "skipFiles": ["<node_internals>/**"], "program": "${workspaceFolder}/node_modules/next/dist/server/next-server.js", "args": ["dev"], "preLaunchTask": "tsc: build - tsconfig.json", "outFiles": ["${workspaceFolder}/dist/**/*.js"], "resolveSourceMapLocations": ["${workspaceFolder}/**", "!**/node_modules/**"], "env": {"NODE_ENV": "development"}}, {"type": "node", "request": "launch", "name": "Debug Express", "skipFiles": ["<node_internals>/**"], "program": "${workspaceFolder}/src/server.ts", "preLaunchTask": "tsc: build - tsconfig.json", "outFiles": ["${workspaceFolder}/dist/**/*.js"], "resolveSourceMapLocations": ["${workspaceFolder}/**", "!**/node_modules/**"], "env": {"NODE_ENV": "development"}}, {"type": "node", "request": "attach", "name": "Attach to Process", "port": 9229, "restart": true, "sourceMaps": true}]}