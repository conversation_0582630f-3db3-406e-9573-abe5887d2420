import type { Attribute } from '@strapi/strapi';

export type StrapiAdminPermission = Attribute.GetValues<"admin::permission">;
export type StrapiAdminUser = Attribute.GetValues<"admin::user">;
export type StrapiAdminRole = Attribute.GetValues<"admin::role">;
export type StrapiAdminApiToken = Attribute.GetValues<"admin::api-token">;
export type StrapiAdminApiTokenPermission = Attribute.GetValues<"admin::api-token-permission">;
export type StrapiAdminTransferToken = Attribute.GetValues<"admin::transfer-token">;
export type StrapiAdminTransferTokenPermission = Attribute.GetValues<"admin::transfer-token-permission">;
export type StrapiPluginUploadFile = Attribute.GetValues<"plugin::upload.file">;
export type StrapiPluginUploadFolder = Attribute.GetValues<"plugin::upload.folder">;
export type StrapiPluginContentReleasesRelease = Attribute.GetValues<"plugin::content-releases.release">;
export type StrapiPluginContentReleasesReleaseAction = Attribute.GetValues<"plugin::content-releases.release-action">;
export type StrapiPluginI18NLocale = Attribute.GetValues<"plugin::i18n.locale">;
export type StrapiPluginUsersPermissionsPermission = Attribute.GetValues<"plugin::users-permissions.permission">;
export type StrapiPluginUsersPermissionsRole = Attribute.GetValues<"plugin::users-permissions.role">;
export type StrapiPluginUsersPermissionsUser = Attribute.GetValues<"plugin::users-permissions.user">;
export type StrapiCity = Attribute.GetValues<"api::city.city">;
export type StrapiCountry = Attribute.GetValues<"api::country.country">;
export type StrapiFilterCategory = Attribute.GetValues<"api::filter-category.filter-category">;
export type StrapiFundamentalsBalanceSheet = Attribute.GetValues<"api::fundamentals-balance-sheet.fundamentals-balance-sheet">;
export type StrapiFundamentalsCashFlow = Attribute.GetValues<"api::fundamentals-cash-flow.fundamentals-cash-flow">;
export type StrapiFundamentalsIncomeStatement = Attribute.GetValues<"api::fundamentals-income-statement.fundamentals-income-statement">;
export type StrapiHistoricalDividend = Attribute.GetValues<"api::historical-dividend.historical-dividend">;
export type StrapiHistoricalSplit = Attribute.GetValues<"api::historical-split.historical-split">;
export type StrapiListOfTicker = Attribute.GetValues<"api::list-of-ticker.list-of-ticker">;
export type StrapiLocationFilter = Attribute.GetValues<"api::location-filter.location-filter">;
export type StrapiLogOfTicker = Attribute.GetValues<"api::log-of-ticker.log-of-ticker">;
export type StrapiPrivacyPolicy = Attribute.GetValues<"api::privacy-policy.privacy-policy">;
export type StrapiProduct = Attribute.GetValues<"api::product.product">;
export type StrapiProductsPrice = Attribute.GetValues<"api::products-price.products-price">;
export type StrapiProductsCharacteristics = Attribute.GetValues<"api::products-characteristics.products-characteristics">;
export type StrapiRegion = Attribute.GetValues<"api::region.region">;
export type StrapiSector = Attribute.GetValues<"api::sector.sector">;
export type StrapiState = Attribute.GetValues<"api::state.state">;
export type StrapiStatisticsOfFilter = Attribute.GetValues<"api::statistics-of-filter.statistics-of-filter">;
export type StrapiStatisticsOfTicker = Attribute.GetValues<"api::statistics-of-ticker.statistics-of-ticker">;
export type StrapiSubRegion = Attribute.GetValues<"api::sub-region.sub-region">;
export type StrapiTermsUse = Attribute.GetValues<"api::terms-use.terms-use">;
export type StrapiWatchlist = Attribute.GetValues<"api::watchlist.watchlist">;
export type StrapiWalletItem = Attribute.GetValues<"api::wallet-item.wallet-item">;
export type StrapiWalletTransaction = Attribute.GetValues<"api::wallet-transaction.wallet-transaction">;
export type StrapiWallet = Attribute.GetValues<"api::wallet.api::wallet">;
