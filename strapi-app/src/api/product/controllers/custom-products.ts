import { factories } from '@strapi/strapi';
import Stripe from "stripe";
import type { StrapiProduct, StrapiProductsPrice } from '../../../../types/generated/types';

export default factories.createCoreController('api::product.product', () => ({
    async getCustomerToken(ctx) {
        let { id, priceId } = ctx.request.query;
        //Create stripe API client using the secret key env variable
        const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string, {});
        // Find the user by id.
        const user = await strapi.query('plugin::users-permissions.user').findOne({ where: { id: id } });

        const {FRONT_END_APP} = process.env

        if (user && !user.blocked) {

          if (user.stripe_customer_id) {
              const customerInBase = await stripe.customers.retrieve(user.stripe_customer_id);
              const session = await stripe.checkout.sessions.create({
                  success_url: FRONT_END_APP,
                  customer: customerInBase.id,
                  line_items: [
                      {
                          price: priceId,
                          quantity: 1,
                      },
                  ],
                  mode: 'subscription'
              });
              if (session && session.url) {
                  return session.url;
              }
            } else {

                const customer = await stripe.customers.create({ email: user.email });

                if (customer) {
                    const stripe_customer_id = customer.id;
                    await strapi.query('plugin::users-permissions.user').update({ where: { id: id }, data: { stripe_customer_id } });
                    const session = await stripe.checkout.sessions.create({
                        success_url: FRONT_END_APP,
                        customer: stripe_customer_id,
                        line_items: [
                            {
                                price: priceId,
                                quantity: 1,
                            },
                        ],
                        mode: 'subscription'
                    });
                    if (session && session.url) {
                        return session.url;
                    }
                }
            }
        }
        return undefined;
    },

    async listAllProductsCharacteristics(ctx) {
        let { language } = ctx.request.params;

        const allProductsCharacteristics = await strapi.entityService.findMany('api::product.product', {
            filters: { active: true },
            locale: language,
            populate: { products_characteristics: { filters: { is_enable: true } }, localizations: { fields: 'id', filters: { locale: 'en' } } }
        }) as StrapiProduct[];

        allProductsCharacteristics.map(product => {
            if (product.localizations.length > 0) {
                product.id = product.localizations[0].id;
            }
        });

        const allProductsCharacteristicsSorted = allProductsCharacteristics.sort((a, b) => b.products_characteristics.length - a.products_characteristics.length);

        let allProductsCharacteristicsReturn = [];
        let firstProductCharacteristicsLength = 0;
        let firstProductName = '';

        for (const [index, product] of allProductsCharacteristicsSorted.entries()) {
            allProductsCharacteristicsReturn.push({ [product.name]: { 'prices': [], 'features': [] } });

            const productPrices = await strapi.entityService.findMany('api::products-price.products-price', {
                filters: { active: true, $and: [{ product: { id: { $eq: product.id } } }] },
                populate: { product: { filters: { active: true } } }
            }) as StrapiProductsPrice[];

            if (productPrices.length === 0) {
                allProductsCharacteristicsReturn[index][product.name].prices.push({ id_stripe: '', interval: '', amount: 0, currency: '' });
            } else {
                for (let i = 0; i < productPrices.length; i++) {
                    allProductsCharacteristicsReturn[index][product.name].prices.push({ id_stripe: productPrices[i].id_stripe, interval: productPrices[i].interval, amount: productPrices[i].amount, currency: productPrices[i].currency });
                }
            }

            if (index == 0) {
                product.products_characteristics.sort((a, b) => Number(a.id) - Number(b.id));
                firstProductCharacteristicsLength = product.products_characteristics.length;
                firstProductName = product.name;
                for (let i = 0; i < firstProductCharacteristicsLength; i++) {
                    allProductsCharacteristicsReturn[index][product.name].features.push({ [product.products_characteristics[i].characteristic]: true });
                }
            } else {
                for (let i = 0; i < firstProductCharacteristicsLength; i++) {
                    const productName = Object.keys(allProductsCharacteristicsReturn[0][firstProductName].features[i])[0];
                    const productFound = product.products_characteristics.some(pc => pc.characteristic === productName);
                    allProductsCharacteristicsReturn[index][product.name].features.push({ [productName]: productFound });
                }
            }
        }

        ctx.send(allProductsCharacteristicsReturn);
    }
}));

