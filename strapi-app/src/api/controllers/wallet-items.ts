import { StrapiWalletTransaction } from "@generated/types";
import { TransactionsResults } from "./transaction-results";

export class WalletItemsResults {
  transactionResults: TransactionsResults
  wallet_id: string

  constructor(wallet_id: string) {
    this.wallet_id = wallet_id;

  }

  async execute(): Promise<{ walletItems: any[], operations: any }> {
    this.transactionResults = new TransactionsResults(this.wallet_id);
    await this.transactionResults.getTransactions();

    const  { transactions }  = this.transactionResults

    this.transactionResults.groupTransactions(transactions);

    const groupedOperations: StrapiWalletTransaction[] = this.transactionResults.groupedTransactionsType;

    const keys_tickers = Object.keys(this.transactionResults.groupedTransactionsTickers);

    const walletItems = [];

    for (let i = 0; i < keys_tickers.length; i++) {
      const key = keys_tickers[i];

      const item = this.calculateWalletItemsTickers(key);

      walletItems.push(item);
    }

    const operations = this.calculateWalletItemsOperations(groupedOperations);

    return {
      walletItems,
      operations
    };
  }

  calculateWalletItemsTickers(ticker_id: string) {
    const groupedTickers = this.transactionResults.groupedTransactionsTickers;

    const item  = {
      avg_price: 0,
      qt_tickers: 0,
      sum_qt_buy: 0,
      current_price: 0,
      current_casted_price: 0,
      sell_qt_tickers: 0,
      sum_buy_casted_price: 0,
      sum_sell_casted_price: 0,
      sum_current_casted_price: 0,
      sum_casted_price: 0,
      total_avg_price: 0,
      valuation: 0,
      wallet_percent: 0,
      total_current_casted_price: 0,
      unrealized_trade: 0,
      realized_trade: 0,
      list_of_ticker: null,
      wallet: null,
      symbol_code: '',
      currency: '',
    }

    for (let i = 0; i < groupedTickers[ticker_id].length; i++) {
      const transaction = groupedTickers[ticker_id][i];

      if (transaction.type === 'BUY') {
        item.sum_qt_buy +=  transaction.quantity;
        item.sum_buy_casted_price += transaction.casted_price*transaction.quantity;
        item.sum_casted_price += transaction.casted_price*transaction.quantity;
        item.qt_tickers += transaction.quantity;
      } else {
        item.qt_tickers -= transaction.quantity;
        item.sum_casted_price -= transaction.casted_price*transaction.quantity;
        item.sum_sell_casted_price += transaction.casted_price*transaction.quantity
        item.sell_qt_tickers += transaction.quantity;
      }
    }

    item.avg_price = item.sum_qt_buy > 0
      ? item.sum_buy_casted_price / item.sum_qt_buy
      : 0;

    item.realized_trade = item.sum_sell_casted_price - item.sell_qt_tickers*item.avg_price;

    const last_item = groupedTickers[ticker_id][groupedTickers[ticker_id].length - 1];
    item.current_price = last_item.current_price;
    item.current_casted_price = last_item.current_casted_price;
    item.wallet = groupedTickers[ticker_id][0].wallet;
    item.list_of_ticker = groupedTickers[ticker_id][0].list_of_ticker;
    item.currency = groupedTickers[ticker_id][0].wallet.currency;
    item.total_current_casted_price = item.qt_tickers * last_item.current_casted_price;
    item.total_avg_price = item.qt_tickers * item.avg_price;
    item.valuation = item.total_avg_price && (item.total_current_casted_price*100)/(item.total_avg_price) - 100;
    item.unrealized_trade = item.total_current_casted_price - item.total_avg_price;
    item.sum_current_casted_price = item.qt_tickers * last_item.current_casted_price;
    return item;

  }

  calculateWalletItemsOperations(transactions: StrapiWalletTransaction[]) {

    const item = {
      balance: 0,
      dividends_paid: 0
    }

    for (let i = 0; i < transactions.length; i++) {
      const transaction = transactions[i];

      item.balance += (transaction.casted_price*(transaction.type === 'DEPOSIT' ? 1 : -transaction.quantity));

      if (transaction.type === 'DIVIDENDS_PAID') {
        item.dividends_paid += transaction.casted_price;
        item.balance += transaction.casted_price;
      }
    }

    return item;

  }
}
