{"kind": "collectionType", "collectionName": "statistics_of_apis", "info": {"singularName": "statistics-of-api", "pluralName": "statistics-of-apis", "displayName": "Statistics of API", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"request_date": {"type": "date", "required": true}, "latest_api": {"type": "enumeration", "enum": ["eod<PERSON>d", "twelve_data", "others"], "required": true}, "api_credit_consumption": {"type": "integer", "default": 0, "required": true}, "request_url": {"type": "text"}, "endpoint_type": {"type": "enumeration", "enum": ["symbol_list", "fundamentals", "dividends", "splits", "price"]}}}