{"kind": "collectionType", "collectionName": "historical_prices", "info": {"singularName": "historical-price", "pluralName": "historical-prices", "displayName": "Historical Price", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"date": {"type": "date", "required": true}, "open": {"type": "float"}, "high": {"type": "float"}, "low": {"type": "float"}, "close": {"type": "float"}, "adjusted_close": {"type": "float"}, "volume": {"type": "biginteger"}, "ticker_internal": {"type": "relation", "relation": "oneToOne", "target": "api::list-of-ticker.list-of-ticker"}}}