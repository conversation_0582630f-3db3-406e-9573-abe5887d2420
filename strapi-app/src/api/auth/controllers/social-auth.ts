// src/api/auth/controllers/social-auth.ts

import type { Strapi } from '@strapi/strapi';
import { sanitize } from '@strapi/utils'; // <-- 1. IMPORTAMOS O UTILITÁRIO DE SANITIZAÇÃO
import axios from 'axios';
import type { Context } from 'koa';

// Mapeia provedores para seus respectivos endpoints de 'userinfo'
const PROVIDER_ENDPOINTS = {
  google: 'https://www.googleapis.com/oauth2/v3/userinfo',
};

// Interface para o perfil do usuário retornado pelo provedor
interface UserProfile {
  email: string;
  username?: string;
  name?: string;
  picture?: string;
}

export default ({ strapi }: { strapi: Strapi }) => ({
  async socialLogin(ctx: Context) {
    const { provider, access_token } = ctx.request.body as { provider?: keyof typeof PROVIDER_ENDPOINTS; access_token?: string };

    if (!provider || !access_token) {
      return ctx.badRequest('Provider ou access_token não fornecidos.');
    }

    const userInfoEndpoint = PROVIDER_ENDPOINTS[provider];
    if (!userInfoEndpoint) {
      return ctx.badRequest('Provedor não suportado ou inválido.');
    }

    try {
      const { data: profile } = await axios.get<UserProfile>(userInfoEndpoint, {
        headers: {
          Authorization: `Bearer ${access_token}`,
        },
      });

      if (!profile.email) {
        return ctx.badRequest('Não foi possível obter o e-mail do provedor.');
      }

      const email = profile.email.toLowerCase();

      const existingUser = await strapi.query('plugin::users-permissions.user').findOne({
        where: { email },
        populate: { role: true },
      });

      let user;

      if (existingUser) {
        strapi.log.info(`Usuário existente [${email}] encontrado. Efetuando login.`);
        user = existingUser;
      } else {
        strapi.log.info(`Criando novo usuário para [${email}].`);

        const advancedSettings = await strapi.store({ type: 'plugin', name: 'users-permissions' }).get({ key: 'advanced' }) as { default_role: string };
        const defaultRole = await strapi.query('plugin::users-permissions.role').findOne({ where: { type: advancedSettings.default_role } });

        user = await strapi.query('plugin::users-permissions.user').create({
          data: {
            username: profile.name || profile.email,
            email: email,
            provider: provider,
            confirmed: true,
            role: defaultRole.id,
            password: '',
          },
        });
      }

      const jwt = strapi.plugin('users-permissions').service('jwt').issue({ id: user.id });

      // 2. A FORMA CORRETA DE SANITIZAR O USUÁRIO NA V4
      const userSchema = strapi.getModel('plugin::users-permissions.user');
      const sanitizedUser = await sanitize.contentAPI.output(user, userSchema, {
        auth: ctx.state.auth,
      });

      return ctx.send({
        jwt,
        user: sanitizedUser, // <-- 3. ENVIAMOS O USUÁRIO JÁ "LIMPO"
      });

    } catch (error) {
      if (axios.isAxiosError(error)) {
        strapi.log.error(error.response?.data, "Erro ao buscar perfil do provedor:");
        return ctx.badRequest('Token de acesso inválido ou expirado.');
      }
      strapi.log.error(error, "Erro inesperado no endpoint socialLogin:");
      return ctx.internalServerError('Ocorreu um erro durante o login social.');
    }
  },
});
