{"kind": "collectionType", "collectionName": "list_of_tickers", "info": {"singularName": "list-of-ticker", "pluralName": "list-of-tickers", "displayName": "List of Tickers", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"symbol_code": {"type": "string", "required": true, "maxLength": 50, "pluginOptions": {}}, "country_code": {"type": "string", "required": false, "maxLength": 10, "pluginOptions": {}}, "exchange_code": {"type": "string", "required": true, "maxLength": 50, "pluginOptions": {}}, "name": {"type": "text", "pluginOptions": {}}, "primary_ticker_eodhd": {"type": "string", "maxLength": 50, "pluginOptions": {}, "unique": true}, "primary_ticker_twelve_data": {"type": "string", "maxLength": 50, "pluginOptions": {}}, "log_eodhd": {"type": "text", "pluginOptions": {}}, "is_enable": {"type": "integer", "max": 1, "min": 0, "required": true, "default": 1, "pluginOptions": {}}, "sector": {"type": "relation", "relation": "oneToOne", "target": "api::sector.sector", "useJoinTable": false}, "currency_code": {"type": "string"}, "isin": {"type": "string"}, "type": {"type": "string"}, "another_symbol_codes": {"type": "string"}, "url_endpoint": {"type": "string"}, "fundamental_data_last_updated": {"type": "date"}, "reason_not_enable": {"type": "enumeration", "enum": ["Symbol not found", "Price not found", "API 404 response", "Other"]}}}