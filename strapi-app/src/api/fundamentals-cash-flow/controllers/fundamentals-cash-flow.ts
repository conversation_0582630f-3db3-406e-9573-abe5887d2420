/**
 * fundamentals-cash-flow controller
 */

import { factories } from '@strapi/strapi';

export default factories.createCoreController('api::fundamentals-cash-flow.fundamentals-cash-flow', ({strapi}) => ({
    async confirmOrder (ctx, next) {
    ctx.body = 'ok';
  },
    async find (ctx, next) {
        // destructure to get `data` and `meta` which strapi returns by default
        const {data, meta} = await super.find(ctx)

        // perform any other custom action
        return {data, meta}
    }
}));
