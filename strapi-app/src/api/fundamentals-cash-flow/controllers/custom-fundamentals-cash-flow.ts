/**
 * custom-fundamentals-cash-flow controller
 */

import { factories } from '@strapi/strapi';

export default factories.createCoreController('api::fundamentals-cash-flow.fundamentals-cash-flow', () => ({
  async findOne(ctx) {
    const { ticker_internal_id } = ctx.request.params

    const entry = await strapi.db.query('api::fundamentals-cash-flow.fundamentals-cash-flow').findOne({
      where: { ticker_internal: ticker_internal_id, document_type_year_or_quarter: 'y', document_current: 1 },
      orderBy: { document_date: 'DESC' },
    });

    ctx.send(entry)
  },
  async find(ctx) {
    const { ticker_internal_id } = ctx.request.params
    const { limit = 5 } = ctx.request.query

    const current_date = new Date()

    const start_date = `${current_date.getFullYear() - limit}-01-01`

    const entry = await strapi.db.query('api::fundamentals-cash-flow.fundamentals-cash-flow').findMany({
      where: { ticker_internal: ticker_internal_id, document_type_year_or_quarter: 'q',
        document_date: {
          $gte: start_date,
        }
      },
      orderBy: { document_date: 'DESC' },
    });

    ctx.send(entry)
  },

  async findYear(ctx) {
    const { ticker_internal_id } = ctx.request.params
    const { limit = 5 } = ctx.request.query

    const current_date = new Date()

    const start_date = `${current_date.getFullYear() - limit}-01-01`

    const entry = await strapi.db.query('api::fundamentals-cash-flow.fundamentals-cash-flow').findMany({
      where: { ticker_internal: ticker_internal_id, document_type_year_or_quarter: 'y',
        document_date: {
          $gte: start_date,
        }
      },
      orderBy: { document_date: 'DESC' },
    });

    ctx.send(entry)
  },
}));
