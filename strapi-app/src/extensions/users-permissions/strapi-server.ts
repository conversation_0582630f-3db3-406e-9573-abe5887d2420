import { errors, sanitize, validateYupSchema, yup } from '@strapi/utils';
import crypto from 'crypto';
import _ from 'lodash';
import { email_template } from '../../utils/email_reset_password';

const { ApplicationError, ValidationError, ForbiddenError } = errors;

const sanitizeUser = (user, ctx) => {
  const { auth } = ctx.state;
  const userSchema = strapi.getModel('plugin::users-permissions.user');

  return sanitize.contentAPI.output(user, userSchema, { auth });
};

const getService = (name) => {
  return strapi.plugin('users-permissions').service(name);
};

const forgotPasswordSchema = yup
  .object({
    email: yup.string().email().required(),
  })
  .noUnknown();

const  validateForgotPasswordBody = validateYupSchema(forgotPasswordSchema)

module.exports = (plugin) => {
  plugin.controllers.auth.forgotPassword = async (ctx) => {
    const { email } = await validateForgotPasswordBody(ctx.request.body);

    const pluginStore = await strapi.store({ type: 'plugin', name: 'users-permissions' });

    const emailSettings = await pluginStore.get({ key: 'email' });
    const advancedSettings = await pluginStore.get({ key: 'advanced' });

    // Find the user by email.
    const user = await strapi
      .query('plugin::users-permissions.user')
      .findOne({ where: { email: email.toLowerCase() } });

    if (!user || user.blocked) {
      throw new ValidationError('User not found');
    }

    // Generate random token.
    const userInfo = await sanitizeUser(user, ctx);

    const resetPasswordToken = crypto.randomBytes(64).toString('hex');

    const language = user.language || 'fr'

    const resetPasswordSettings = _.get(emailSettings, 'reset_password.options', {});

    const emailTemplate = email_template[language]

    const link = `${process.env.FRONT_END_APP}/new-password?confirmation=${resetPasswordToken}`

    const data = { link }

    try {
      await strapi.plugins['email'].services.email.sendTemplatedEmail(
        {
          to: user.email,
          from: 'Tibainvest',
          replyTo: '<EMAIL>'
        },
          emailTemplate,
        {
          data
        }
      );
    } catch(e) {
      console.log('error', e)
    }


    // NOTE: Update the user before sending the email so an Admin can generate the link if the email fails


    // Send an email to the user.

    await getService('user').edit(user.id, { resetPasswordToken });

    ctx.send({ ok: true });
  },

  plugin.controllers.user.updateMe = async (ctx) => {
    if (!ctx.state.user || !ctx.state.user.id) {
      return ctx.response.status = 401
    }

    await strapi.query('plugin::users-permissions.user').update({
      where: {
        id: ctx.state.user.id
      },
      data: ctx.request.body
    })

    return ctx.response.status = 201
  }

  plugin.routes['content-api'].routes.push({
    method: 'PUT',
    path: '/user/me',
    handler: "user.updateMe",
    config: {
      prefix: '',
      policies: []
    }
  })



  return plugin
}
