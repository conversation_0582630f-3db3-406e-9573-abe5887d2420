{"name": "lambda-tibainvest", "version": "1.0.0", "main": "index.js", "license": "MIT", "private": true, "devDependencies": {"@jest/globals": "^29.7.0", "@types/aws-lambda": "^8.10.119", "@types/axios": "^0.14.0", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.0", "@types/lodash": "^4.14.196", "@types/node": "^20.4.2", "@types/ramda": "^0.29.3", "@types/react": "^18.2.48", "@types/react-dom": "^18.2.18", "@types/uuid": "^9.0.2", "autoprefixer": "^10.4.21", "jest": "^29.7.0", "nodemon": "^3.1.9", "ts-jest": "^29.1.0", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.6"}, "scripts": {"start": "next start -p 3000", "start:agenda": "ts-node src/scripts/start-agenda.ts", "dev": "next dev -p 3000", "dev:agenda": "nodemon src/scripts/start-agenda.ts", "build": "next build", "start:all": "npm run start:agenda & npm run dev", "watch": "tsc -w", "test-db": "ts-node src/test-db.ts", "test:unit": "jest --testPathIgnorePatterns='.*\\.integration\\.test\\.ts$'", "test:integration": "TEST_TYPE=integration jest --config=jest.config.js --testMatch='**/*.integration.test.ts' --runInBand --detectOpenHandles --forceExit", "test:all": "npm run test:unit && npm run test:integration"}, "dependencies": {"@aws-sdk/client-dynamodb": "^3.787.0", "@aws-sdk/client-lambda": "^3.386.0", "@aws-sdk/client-s3": "^3.373.0", "@aws-sdk/client-sqs": "^3.425.0", "@aws-sdk/lib-dynamodb": "^3.369.0", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.11", "agenda": "^5.0.0", "axios": "^1.4.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.21.2", "lodash": "^4.17.21", "lucide-react": "^0.510.0", "moment": "^2.29.4", "mysql2": "^3.14.1", "next": "^14.0.4", "ramda": "^0.29.0", "react": "^18.2.0", "react-dom": "^18.2.0", "recharts": "^2.15.3", "sequelize": "^6.32.1", "tailwind-merge": "^3.3.0", "tailwindcss": "^3.4.0", "tailwindcss-animate": "^1.0.7", "uuid": "^9.0.0"}}