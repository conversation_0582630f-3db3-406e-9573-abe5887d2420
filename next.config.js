/** @type {import('next').NextConfig} */
const nextConfig = {
    reactStrictMode: true,
    swcMinify: true,
    webpack: (config, {isServer}) => {
        // Exclude Node.js modules from client-side bundle
        if (!isServer) {
            config.resolve.fallback = {
                ...config.resolve.fallback,
                fs: false,
                net: false,
                dns: false,
                tls: false,
                child_process: false,
                "mongodb-client-encryption": false,
            };

            // Externalize server-side packages for client-side
            config.externals = config.externals || [];
            config.externals.push({
                mongodb: "commonjs mongodb",
                agenda: "commonjs agenda",
                sequelize: "commonjs sequelize",
                mysql2: "commonjs mysql2",
                "pg-hstore": "commonjs pg-hstore",
            });
        }

        return config;
    },
};

module.exports = nextConfig;
