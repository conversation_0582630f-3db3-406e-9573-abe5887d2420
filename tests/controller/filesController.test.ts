import {processS3Files} from "../../src/controller/filesController";
import {ListOfTickers} from "../../src/entities/consolidated_data/ListOfTickers";
import {FundamentalDataRepository} from "../../src/repositories/implements/FundamentaDataRepository";
import {DynamoController} from "../../src/controller/dynamoController";
import {Op} from "sequelize";

// Mock dependencies
jest.mock("../../src/entities/consolidated_data/ListOfTickers", () => {
    return {
        ListOfTickers: {
            findAll: jest.fn(),
        },
    };
});

jest.mock("../../src/repositories/implements/FundamentaDataRepository", () => {
    return {
        FundamentalDataRepository: jest.fn().mockImplementation(() => {
            return {
                get: jest.fn().mockResolvedValue({ticker_internal_id: "123", Financials: {}}),
            };
        }),
    };
});

jest.mock("../../src/controller/dynamoController", () => {
    return {
        DynamoController: jest.fn().mockImplementation(() => {
            return {
                saveFundamentalData: jest.fn().mockResolvedValue({}),
            };
        }),
    };
});

jest.mock("../../src/lib/agenda", () => ({
    addLogJobExecution: jest.fn().mockResolvedValue("mock-log-id"),
}));

// Mock all the controllers and entities used by filesController
jest.mock("../../src/controller/balanceSheetController", () => ({
    BalanceSheetController: jest.fn().mockImplementation(() => ({
        parseData: jest.fn().mockResolvedValue({
            quantity_of_balance_sheet_year: 5,
            quantity_of_balance_sheet_quarter: 4,
            start_of_balance_sheet_year: "2019",
            end_of_balance_sheet_year: "2023",
            start_of_balance_sheet_quarter: "2023-Q1",
            end_of_balance_sheet_quarter: "2023-Q4",
        }),
    })),
}));

jest.mock("../../src/controller/incomeStatementController", () => ({
    IncomeStatementController: jest.fn().mockImplementation(() => ({
        parseData: jest.fn().mockResolvedValue({
            quantity_of_income_statement_year: 5,
            quantity_of_income_statement_quarter: 4,
            start_of_income_statement_year: "2019",
            end_of_income_statement_year: "2023",
            start_of_income_statement_quarter: "2023-Q1",
            end_of_income_statement_quarter: "2023-Q4",
        }),
    })),
}));

jest.mock("../../src/controller/CashFlowController", () => ({
    CashFlowController: jest.fn().mockImplementation(() => ({
        parseData: jest.fn().mockResolvedValue({
            quantity_of_cash_flow_year: 5,
            quantity_of_cash_flow_quarter: 4,
            start_of_cash_flow_year: "2019",
            end_of_cash_flow_year: "2023",
            start_of_cash_flow_quarter: "2023-Q1",
            end_of_cash_flow_quarter: "2023-Q4",
        }),
    })),
}));

jest.mock("../../src/controller/logsController", () => ({
    LogsController: jest.fn().mockImplementation(() => ({
        tickerUpdatedEODHD: jest.fn(),
        dynamoDBQueued: jest.fn(),
    })),
}));

describe("filesController", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe("findFiles", () => {
        it("should process provided tickers and call fundamental data repository", async () => {
            // Arrange
            const mockTickers = [
                {id: 1, symbol_code: "AAPL", primary_ticker_eodhd: "AAPL.US"},
                {id: 2, symbol_code: "MSFT", primary_ticker_eodhd: "MSFT.US"},
            ];

            // Act
            await processS3Files(mockTickers);

            // Assert
            // Verify that the mocked constructor was called (instances were created)
            expect(FundamentalDataRepository).toHaveBeenCalledTimes(1);
            expect(DynamoController).toHaveBeenCalledTimes(1);

            // Get the mock instances that were created inside processS3Files
            const fundamentalDataMockInstance = (FundamentalDataRepository as jest.Mock).mock.results[0].value;

            // Verify fundamentalData.get was called for each ticker
            expect(fundamentalDataMockInstance.get).toHaveBeenCalledTimes(2);
            expect(fundamentalDataMockInstance.get).toHaveBeenCalledWith("fundamentals_AAPL.US_eod.json");
            expect(fundamentalDataMockInstance.get).toHaveBeenCalledWith("fundamentals_MSFT.US_eod.json");
        });
    });
});
