import {apiEOD} from "../../src/services/apiEOD"; //"../src/services/apiEOD";
import {ExchangeAPI, TickerTypeDataBase} from "../../src/utils/types/EODHD/exchange_countries";
import {populateTickersAPI_EOD, processExchangeTickers, populateListOfTickers} from "../../src/controller/tickersController";
import {beforeEach, describe, expect, it, jest} from "@jest/globals";

// Mock the dependencies
jest.mock("../../src/services/apiEOD");
jest.mock("../../src/repositories/implements/ListOfTickersRepository");
jest.mock("../../src/lib/agenda", () => ({
    addLogJobExecution: jest.fn().mockResolvedValue("mock-log-id"),
}));

describe("populateTickersAPI_EOD - Unit Tests", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("should fetch tickers from all exchanges and return combined data", async () => {
        // Mock response data for each exchange
        const mockNYSE = [{Code: "KO", Name: "The Coca-Cola Company", Country: "USA", Exchange: "NYSE", Currency: "USD", Type: "Common Stock", Isin: "US1912161007"}];
        const mockNASDAQ = [{Code: "MSFT", Name: "Microsoft Corp", Country: "USA", Exchange: "NASDAQ", Currency: "USD", Type: "Common Stock", Isin: "US5949181045"}];
        const mockBVMF = [{Code: "PETR3", Name: "Petrobras", Country: "Brazil", Exchange: "SA", Currency: "BRL", Type: "Common Stock", Isin: "BRPETRACNOR9"}];
        const mockXOSL = [{Code: "EQNR", Name: "Equinor ASA", Country: "Norway", Exchange: "OL", Currency: "NOK", Type: "Common Stock", Isin: "NO0010096985"}];
        const mockXPAR = [{Code: "AF", Name: "Air France-KLM SA", Country: "France", Exchange: "PA", Currency: "EUR", Type: "Common Stock", Isin: "FR0000031122"}];
        const mockXETR = [{Code: "SAP", Name: "SAP SE", Country: "Germany", Exchange: "XETRA", Currency: "EUR", Type: "Common Stock", Isin: "DE0007164600"}];
        const mockXBRU = [{Code: "ABI", Name: "Anheuser Busch InBev SA NV", Country: "Belgium", Exchange: "BR", Currency: "EUR", Type: "Common Stock", Isin: "BE0974293251"}];
        const mockXAMS = [{Code: "ASML", Name: "ASML Holding NV", Country: "Netherlands", Exchange: "AS", Currency: "EUR", Type: "Common Stock", Isin: "NL0010273215"}];
        const mockXLIS = [{Code: "EDP", Name: "EDP - Energias de Portugal S.A.", Country: "Portugal", Exchange: "LS", Currency: "EUR", Type: "Common Stock", Isin: "PTEDP0AM0009"}];
        const mockXDUB = [{Code: "RYA", Name: "Ryanair Holdings plc", Country: "Ireland", Exchange: "IR", Currency: "EUR", Type: "Common Stock", Isin: "IE00BYTBXV33"}];
        const mockLSE = [{Code: "HSBA", Name: "HSBC Holdings PLC", Country: "UK", Exchange: "LSE", Currency: "GBX", Type: "Common Stock", Isin: "GB0005405286"}];

        // Setup mock implementations for each exchange
        (apiEOD.populateTickers as jest.Mock).mockImplementation((exchange) => {
            switch (exchange) {
                case ExchangeAPI.USA.NYSE.getExchangeCode():
                    return Promise.resolve({data: mockNYSE});
                case ExchangeAPI.USA.NASDAQ.getExchangeCode():
                    return Promise.resolve({data: mockNASDAQ});
                case ExchangeAPI.BRAZIL.BOVESPA.getExchangeCode():
                    return Promise.resolve({data: mockBVMF});
                case ExchangeAPI.NORWAY.OSLO.getExchangeCode():
                    return Promise.resolve({data: mockXOSL});
                case ExchangeAPI.FRANCE.EURONEXT.getExchangeCode():
                    return Promise.resolve({data: mockXPAR});
                case ExchangeAPI.GERMANY.XETRA.getExchangeCode():
                    return Promise.resolve({data: mockXETR});
                case ExchangeAPI.BELGIUM.EURONEXT.getExchangeCode():
                    return Promise.resolve({data: mockXBRU});
                case ExchangeAPI.NETHERLANDS.EURONEXT.getExchangeCode():
                    return Promise.resolve({data: mockXAMS});
                case ExchangeAPI.PORTUGAL.EURONEXT.getExchangeCode():
                    return Promise.resolve({data: mockXLIS});
                case ExchangeAPI.IRELAND.EURONEXT.getExchangeCode():
                    return Promise.resolve({data: mockXDUB});
                case ExchangeAPI.UNITED_KINGDOM.LONDON.getExchangeCode():
                    return Promise.resolve({data: mockLSE});
                default:
                    return Promise.resolve({data: []});
            }
        });

        // Call the function
        const result = await populateTickersAPI_EOD();

        // Verify the function was called for each exchange
        expect(apiEOD.populateTickers).toHaveBeenCalledTimes(11);
        expect(apiEOD.populateTickers).toHaveBeenCalledWith(ExchangeAPI.USA.NYSE.getExchangeCode());
        expect(apiEOD.populateTickers).toHaveBeenCalledWith(ExchangeAPI.USA.NASDAQ.getExchangeCode());
        expect(apiEOD.populateTickers).toHaveBeenCalledWith(ExchangeAPI.BRAZIL.BOVESPA.getExchangeCode());
        expect(apiEOD.populateTickers).toHaveBeenCalledWith(ExchangeAPI.NORWAY.OSLO.getExchangeCode());
        expect(apiEOD.populateTickers).toHaveBeenCalledWith(ExchangeAPI.FRANCE.EURONEXT.getExchangeCode());
        expect(apiEOD.populateTickers).toHaveBeenCalledWith(ExchangeAPI.GERMANY.XETRA.getExchangeCode());
        expect(apiEOD.populateTickers).toHaveBeenCalledWith(ExchangeAPI.BELGIUM.EURONEXT.getExchangeCode());
        expect(apiEOD.populateTickers).toHaveBeenCalledWith(ExchangeAPI.NETHERLANDS.EURONEXT.getExchangeCode());
        expect(apiEOD.populateTickers).toHaveBeenCalledWith(ExchangeAPI.PORTUGAL.EURONEXT.getExchangeCode());
        expect(apiEOD.populateTickers).toHaveBeenCalledWith(ExchangeAPI.IRELAND.EURONEXT.getExchangeCode());
        expect(apiEOD.populateTickers).toHaveBeenCalledWith(ExchangeAPI.UNITED_KINGDOM.LONDON.getExchangeCode());

        // Verify the result contains data from all exchanges
        expect(result).toEqual({
            NYSE: mockNYSE,
            NASDAQ: mockNASDAQ,
            SA: mockBVMF,
            OL: mockXOSL,
            PA: mockXPAR,
            XETRA: mockXETR,
            BR: mockXBRU,
            AS: mockXAMS,
            LS: mockXLIS,
            IR: mockXDUB,
            LSE: mockLSE,
        });
    });

    it("should fetch tickers only for specified exchanges when exchangeCodes parameter is provided", async () => {
        // Mock response data for specific exchanges
        const mockBVMF = [{Code: "PETR3", Name: "Petrobras", Country: "Brazil", Exchange: "SA", Currency: "BRL", Type: "Common Stock", Isin: "BRPETRACNOR9"}];
        const mockXETR = [{Code: "SAP", Name: "SAP SE", Country: "Germany", Exchange: "XETRA", Currency: "EUR", Type: "Common Stock", Isin: "DE0007164600"}];

        // Setup mock implementations for specific exchanges only
        (apiEOD.populateTickers as jest.Mock).mockImplementation((exchange) => {
            switch (exchange) {
                case ExchangeAPI.BRAZIL.BOVESPA.getExchangeCode():
                    return Promise.resolve({data: mockBVMF});
                case ExchangeAPI.GERMANY.XETRA.getExchangeCode():
                    return Promise.resolve({data: mockXETR});
                default:
                    return Promise.resolve({data: []});
            }
        });

        // Call the function with specific exchange codes
        const exchangeCodes = [ExchangeAPI.BRAZIL.BOVESPA.getExchangeCode(), ExchangeAPI.GERMANY.XETRA.getExchangeCode()];
        const result = await populateTickersAPI_EOD(exchangeCodes);

        // Verify the function was called only for specified exchanges
        expect(apiEOD.populateTickers).toHaveBeenCalledTimes(2);
        expect(apiEOD.populateTickers).toHaveBeenCalledWith(ExchangeAPI.BRAZIL.BOVESPA.getExchangeCode());
        expect(apiEOD.populateTickers).toHaveBeenCalledWith(ExchangeAPI.GERMANY.XETRA.getExchangeCode());

        // Verify the function was NOT called for other exchanges
        expect(apiEOD.populateTickers).not.toHaveBeenCalledWith(ExchangeAPI.USA.NYSE.getExchangeCode());
        expect(apiEOD.populateTickers).not.toHaveBeenCalledWith(ExchangeAPI.USA.NASDAQ.getExchangeCode());

        // Verify the result contains data only from specified exchanges
        expect(result).toEqual({
            SA: mockBVMF,
            XETRA: mockXETR,
        });

        // Verify other exchanges are not in the result
        expect(result.NYSE).toBeUndefined();
        expect(result.NASDAQ).toBeUndefined();
        expect(result.XOSL).toBeUndefined();
    });

    it("should handle invalid exchange codes gracefully", async () => {
        // Setup mock implementation
        (apiEOD.populateTickers as jest.Mock).mockImplementation((exchange) => {
            if (exchange === ExchangeAPI.BRAZIL.BOVESPA.getExchangeCode()) {
                return Promise.resolve({data: [{Code: "PETR3", Name: "Petrobras", Country: "Brazil", Exchange: "SA", Currency: "BRL", Type: "Common Stock", Isin: "BRPETRACNOR9"}]});
            }
            return Promise.resolve({data: []});
        });

        // Call the function with valid and invalid exchange codes
        const exchangeCodes = [ExchangeAPI.BRAZIL.BOVESPA.getExchangeCode(), "INVALID_EXCHANGE"];
        const result = await populateTickersAPI_EOD(exchangeCodes);

        // Verify the function was called only for valid exchanges
        expect(apiEOD.populateTickers).toHaveBeenCalledTimes(1);
        expect(apiEOD.populateTickers).toHaveBeenCalledWith(ExchangeAPI.BRAZIL.BOVESPA.getExchangeCode());

        // Verify the result contains data only from valid exchanges
        expect(result).toEqual({
            SA: [{Code: "PETR3", Name: "Petrobras", Country: "Brazil", Exchange: "SA", Currency: "BRL", Type: "Common Stock", Isin: "BRPETRACNOR9"}],
        });
    });
});

describe("processExchangeTickers - Unit Tests", () => {
    // Test Brazilian exchange (SA)
    describe("Brazilian Exchange (SA)", () => {
        it("should process Brazilian tickers correctly", () => {
            // Mock data for Brazilian exchange
            const mockBrazilianTickers = [
                {Code: "PETR3", Name: "Petrobras", Country: "Brazil", Exchange: "SA", Currency: "BRL", Type: "Common Stock", Isin: "BRPETRACNOR9"},
                {Code: "PETR4", Name: "Petrobras Preferred", Country: "Brazil", Exchange: "SA", Currency: "BRL", Type: "Preferred Stock", Isin: "BRPETRACNPR6"},
                {Code: "VALE3", Name: "Vale", Country: "Brazil", Exchange: "SA", Currency: "BRL", Type: "Common Stock", Isin: "BRVALEACNOR0"},
                {Code: "TAEE11", Name: "Transmissora Aliança de Energia Elétrica S.A", Country: "Brazil", Exchange: "SA", Currency: "BRL", Type: "Common Stock", Isin: "BRTAEECDAM10"},
                {Code: "TAEE3", Name: "Transmissora Aliança de Energia Elétrica S.A", Country: "Brazil", Exchange: "SA", Currency: "BRL", Type: "Common Stock", Isin: "BRTAEEACNOR9"},
                {Code: "TAEE3F", Name: "TAEE3F", Country: "Brazil", Exchange: "SA", Currency: "BRL", Type: "Common Stock", Isin: ""},
                {Code: "TAEE4", Name: "Transmissora Aliança de Energia Elétrica S.A", Country: "Brazil", Exchange: "SA", Currency: "BRL", Type: "Preferred Stock", Isin: " "},
                {Code: "TAEE4F", Name: "TAEE4F", Country: "Brazil", Exchange: "SA", Currency: "BRL", Type: "Common Stock", Isin: ""},
            ];

            // Process the tickers
            const result = processExchangeTickers(mockBrazilianTickers, ExchangeAPI.BRAZIL.BOVESPA.getName(), ExchangeAPI.BRAZIL.BOVESPA.getExchangeCode());

            // Verify results
            expect(result).toHaveLength(3); // Should have 3 entries (PETR3 and VALE3 and TAEE11)

            // Check PETR3 (with PETR4 as another_symbol_codes)
            const petr = result.find((t) => t.symbol_code === "PETR3");
            expect(petr).toBeDefined();
            expect(petr?.country_code).toBe("BRAZIL");
            expect(petr?.exchange_code).toBe(ExchangeAPI.BRAZIL.BOVESPA.getName());
            expect(petr?.primary_ticker_eodhd).toBe("PETR3.SA");
            expect(petr?.isin).toBe("BRPETRACNOR9");
            expect(petr?.type).toBe(TickerTypeDataBase.COMMON_STOCK);
            expect(petr?.another_symbol_codes).toBe("PETR4");

            // Check VALE3
            const vale = result.find((t) => t.symbol_code === "VALE3");
            expect(vale).toBeDefined();
            expect(vale?.country_code).toBe("BRAZIL");
            expect(vale?.exchange_code).toBe(ExchangeAPI.BRAZIL.BOVESPA.getName());
            expect(vale?.primary_ticker_eodhd).toBe("VALE3.SA");
            expect(vale?.isin).toBe("BRVALEACNOR0");
            expect(vale?.type).toBe(TickerTypeDataBase.COMMON_STOCK);

            // Check TAEE11 (unit share)
            const taee11 = result.find((t) => t.symbol_code === "TAEE11");
            expect(taee11).toBeDefined();
            expect(taee11?.country_code).toBe("BRAZIL");
            expect(taee11?.exchange_code).toBe(ExchangeAPI.BRAZIL.BOVESPA.getName());
            expect(taee11?.primary_ticker_eodhd).toBe("TAEE11.SA");
            expect(taee11?.isin).toBe("BRTAEECDAM10");
            expect(taee11?.type).toBe(TickerTypeDataBase.COMMON_STOCK);
            expect(taee11?.another_symbol_codes).toBe("TAEE3;TAEE4");
            // Verify that TAEE3F and TAEE4F were filtered out (null ISINs)
            expect(result.find((t) => t.symbol_code === "TAEE3F")).toBeUndefined();
            expect(result.find((t) => t.symbol_code === "TAEE4F")).toBeUndefined();
        });
    });

    // Test US exchanges (NYSE, NASDAQ)
    describe("US Exchanges (NYSE, NASDAQ)", () => {
        it("should process NYSE tickers correctly", () => {
            // Mock data for NYSE
            const mockNYSETickers = [
                {Code: "KO", Name: "The Coca-Cola Company", Country: "USA", Exchange: "NYSE", Currency: "USD", Type: "Common Stock", Isin: "US1912161007"},
                {Code: "ABR", Name: "Arbor Realty Trust", Country: "USA", Exchange: "NYSE", Currency: "USD", Type: "Common Stock", Isin: "US0389231087"},
                {Code: "ABR-P-D", Name: "Arbor Realty Trust Inc | 6.375% Series D Cumulative", Country: "USA", Exchange: "NYSE", Currency: "USD", Type: "Preferred Stock", Isin: ""},
                {Code: "ABR-P-E", Name: "Arbor Realty Trust Inc | 6.25% Series E Cumulative", Country: "USA", Exchange: "NYSE", Currency: "USD", Type: "Preferred Stock", Isin: ""},
                {Code: "ABR-P-F", Name: "Arbor Realty Trust Inc | 6.25% Series F Cumulative", Country: "USA", Exchange: "NYSE", Currency: "USD", Type: "Preferred Stock", Isin: ""},
                {Code: "ABR-PD", Name: "Arbor Realty Trust Inc | 6.375% Series D Cumulative", Country: "USA", Exchange: "NYSE", Currency: "USD", Type: "Preferred Stock", Isin: ""},
                {Code: "ABR-PE", Name: "Arbor Realty Trust Inc | 6.25% Series E Cumulative", Country: "USA", Exchange: "NYSE", Currency: "USD", Type: "Preferred Stock", Isin: ""},
                {Code: "ABR-PF", Name: "Arbor Realty Trust Inc | 6.25% Series F Cumulative", Country: "USA", Exchange: "NYSE", Currency: "USD", Type: "Preferred Stock", Isin: ""},
                {Code: "ACCS", Name: "ACCESS Newswire Inc.", Country: "USA", Exchange: "NYSE MKT", Currency: "USD", Type: "Common Stock", Isin: ""},
                {Code: "JNJ", Name: "Johnson & Johnson", Country: "USA", Exchange: "NYSE", Currency: "USD", Type: "Common Stock", Isin: "US4781601046"},
            ];

            // Process the tickers
            const result = processExchangeTickers(mockNYSETickers, ExchangeAPI.USA.NYSE.getName(), ExchangeAPI.USA.NYSE.getAlternativeExchangeCode());

            // Verify results
            expect(result).toHaveLength(3); // Should have 2 entries (KO, ABR and JNJ)

            // Check KO (with KO-P as another_symbol_codes)
            const ko = result.find((t) => t.symbol_code === "KO");
            expect(ko).toBeDefined();
            expect(ko?.country_code).toBe("USA");
            expect(ko?.exchange_code).toBe(ExchangeAPI.USA.NYSE.getName());
            expect(ko?.primary_ticker_eodhd).toBe(`KO.${ExchangeAPI.USA.NYSE.getAlternativeExchangeCode()}`);
            expect(ko?.isin).toBe("US1912161007");
            expect(ko?.type).toBe(TickerTypeDataBase.COMMON_STOCK);
            expect(ko?.another_symbol_codes).toBe(undefined);

            // Check ABR (with preferred shares as another_symbol_codes)
            const abr = result.find((t) => t.symbol_code === "ABR");
            expect(abr).toBeDefined();
            expect(abr?.country_code).toBe("USA");
            expect(abr?.exchange_code).toBe(ExchangeAPI.USA.NYSE.getName());
            expect(abr?.primary_ticker_eodhd).toBe(`ABR.${ExchangeAPI.USA.NYSE.getAlternativeExchangeCode()}`);
            expect(abr?.isin).toBe("US0389231087");
            expect(abr?.type).toBe(TickerTypeDataBase.COMMON_STOCK);
            expect(abr?.another_symbol_codes).toBe("ABR-P-D;ABR-P-E;ABR-P-F;ABR-PD;ABR-PE;ABR-PF");

            // Verify that preferred shares were not added as separate entries
            expect(result.find((t) => t.symbol_code === "ABR-P-D")).toBeUndefined();
            expect(result.find((t) => t.symbol_code === "ABR-PD")).toBeUndefined();

            // Check ACCS (with null ISIN)
            expect(result.find((t) => t.symbol_code === "ACCS")).toBeUndefined();

            // Check JNJ
            const jnj = result.find((t) => t.symbol_code === "JNJ");
            expect(jnj).toBeDefined();
            expect(jnj?.country_code).toBe("USA");
            expect(jnj?.exchange_code).toBe(ExchangeAPI.USA.NYSE.getName());
            expect(jnj?.primary_ticker_eodhd).toBe(`JNJ.${ExchangeAPI.USA.NYSE.getAlternativeExchangeCode()}`);
            expect(jnj?.isin).toBe("US4781601046");
            expect(jnj?.type).toBe(TickerTypeDataBase.COMMON_STOCK);
        });

        it("should process NASDAQ tickers correctly", () => {
            // Mock data for NASDAQ
            const mockNasdaqTickers = [
                {Code: "MSFT", Name: "Microsoft Corporation", Country: "USA", Exchange: "NASDAQ", Currency: "USD", Type: "Common Stock", Isin: "US5949181045"},
                {Code: "AAPL", Name: "Apple Inc", Country: "USA", Exchange: "NASDAQ", Currency: "USD", Type: "Common Stock", Isin: "US0378331005"},
            ];

            // Process the tickers
            const result = processExchangeTickers(mockNasdaqTickers, ExchangeAPI.USA.NASDAQ.getName(), ExchangeAPI.USA.NASDAQ.getAlternativeExchangeCode());

            // Verify results
            expect(result).toHaveLength(2);

            // Check MSFT
            const msft = result.find((t) => t.symbol_code === "MSFT");
            expect(msft).toBeDefined();
            expect(msft?.country_code).toBe("USA");
            expect(msft?.exchange_code).toBe(ExchangeAPI.USA.NASDAQ.getName());
            expect(msft?.primary_ticker_eodhd).toBe(`MSFT.${ExchangeAPI.USA.NASDAQ.getAlternativeExchangeCode()}`);
            expect(msft?.isin).toBe("US5949181045");
            expect(msft?.type).toBe(TickerTypeDataBase.COMMON_STOCK);

            // Check AAPL
            const aapl = result.find((t) => t.symbol_code === "AAPL");
            expect(aapl).toBeDefined();
            expect(aapl?.country_code).toBe("USA");
            expect(aapl?.exchange_code).toBe(ExchangeAPI.USA.NASDAQ.getName());
            expect(aapl?.primary_ticker_eodhd).toBe(`AAPL.${ExchangeAPI.USA.NASDAQ.getAlternativeExchangeCode()}`);
            expect(aapl?.isin).toBe("US0378331005");
            expect(aapl?.type).toBe(TickerTypeDataBase.COMMON_STOCK);
        });
    });

    // Test European exchanges
    describe("European Exchanges", () => {
        it("should process German XETR tickers correctly", () => {
            // Mock data for XETR
            const mockXetrTickers = [
                {Code: "SAP", Name: "SAP SE", Country: "Germany", Exchange: "XETRA", Currency: "EUR", Type: "Common Stock", Isin: "DE0007164600"},
                {Code: "BMW", Name: "Bayerische Motoren Werke Aktiengesellschaft", Country: "Germany", Exchange: "XETRA", Currency: "EUR", Type: "Common Stock", Isin: "DE0005190003"},
                {Code: "BNP", Name: "BNP Paribas SA", Country: "Germany", Exchange: "XETRA", Currency: "EUR", Type: "Common Stock", Isin: "FR0000131104"},
            ];

            // Process the tickers
            const result = processExchangeTickers(mockXetrTickers, ExchangeAPI.GERMANY.XETRA.getName(), ExchangeAPI.GERMANY.XETRA.getExchangeCode());

            // Verify results
            expect(result).toHaveLength(2);

            // Check SAP
            const sap = result.find((t) => t.symbol_code === "SAP");
            expect(sap).toBeDefined();
            expect(sap?.country_code).toBe("GERMANY");
            expect(sap?.exchange_code).toBe(ExchangeAPI.GERMANY.XETRA.getName());
            expect(sap?.primary_ticker_eodhd).toBe(`SAP.${ExchangeAPI.GERMANY.XETRA.getExchangeCode()}`);
            expect(sap?.isin).toBe("DE0007164600");
            expect(sap?.type).toBe(TickerTypeDataBase.COMMON_STOCK);
        });

        it("should process French XPAR tickers correctly", () => {
            // Mock data for XPAR
            const mockXparTickers = [
                {Code: "AF", Name: "Air France-KLM SA", Country: "France", Exchange: "PA", Currency: "EUR", Type: "Common Stock", Isin: "FR0000031122"},
                {Code: "AIR", Name: "Airbus Group SE", Country: "France", Exchange: "PA", Currency: "EUR", Type: "Common Stock", Isin: "NL0000235190"},
                {Code: "MC", Name: "LVMH Moët Hennessy - Louis Vuitton Société Européenne", Country: "France", Exchange: "PA", Currency: "EUR", Type: "Common Stock", Isin: "FR0000121014"},
            ];

            // Process the tickers
            const result = processExchangeTickers(mockXparTickers, ExchangeAPI.FRANCE.EURONEXT.getName(), ExchangeAPI.FRANCE.EURONEXT.getExchangeCode());

            // Verify results
            expect(result).toHaveLength(3);

            // Check AF
            const af = result.find((t) => t.symbol_code === "AF");
            expect(af).toBeDefined();
            expect(af?.country_code).toBe("FRANCE");
            expect(af?.exchange_code).toBe(ExchangeAPI.FRANCE.EURONEXT.getName());
            expect(af?.primary_ticker_eodhd).toBe(`AF.${ExchangeAPI.FRANCE.EURONEXT.getExchangeCode()}`);
            expect(af?.isin).toBe("FR0000031122");
            expect(af?.type).toBe(TickerTypeDataBase.COMMON_STOCK);
        });

        it("should process Dutch XAMS tickers correctly", () => {
            // Mock data for XAMS
            const mockXamsTickers = [
                {Code: "ASML", Name: "ASML Holding NV", Country: "Netherlands", Exchange: "AS", Currency: "EUR", Type: "Common Stock", Isin: "NL0010273215"},
                {Code: "PHIA", Name: "Koninklijke Philips NV", Country: "Netherlands", Exchange: "AS", Currency: "EUR", Type: "Common Stock", Isin: "NL0000009538"},
            ];

            // Process the tickers
            const result = processExchangeTickers(mockXamsTickers, ExchangeAPI.NETHERLANDS.EURONEXT.getName(), ExchangeAPI.NETHERLANDS.EURONEXT.getExchangeCode());

            // Verify results
            expect(result).toHaveLength(2);

            // Check ASML
            const asml = result.find((t) => t.symbol_code === "ASML");
            expect(asml).toBeDefined();
            expect(asml?.country_code).toBe("NETHERLANDS");
            expect(asml?.exchange_code).toBe(ExchangeAPI.NETHERLANDS.EURONEXT.getName());
            expect(asml?.primary_ticker_eodhd).toBe(`ASML.${ExchangeAPI.NETHERLANDS.EURONEXT.getExchangeCode()}`);
            expect(asml?.isin).toBe("NL0010273215");
            expect(asml?.type).toBe(TickerTypeDataBase.COMMON_STOCK);
        });

        it("should process Belgian XBRU tickers correctly", () => {
            // Mock data for XBRU
            const mockXbruTickers = [
                {Code: "ABI", Name: "Anheuser Busch InBev SA NV", Country: "Belgium", Exchange: "BR", Currency: "EUR", Type: "Common Stock", Isin: "BE0974293251"},
                {Code: "KBC", Name: "KBC Group NV", Country: "Belgium", Exchange: "BR", Currency: "EUR", Type: "Common Stock", Isin: "BE0003565737"},
            ];

            // Process the tickers
            const result = processExchangeTickers(mockXbruTickers, ExchangeAPI.BELGIUM.EURONEXT.getName(), ExchangeAPI.BELGIUM.EURONEXT.getExchangeCode());

            // Verify results
            expect(result).toHaveLength(2);

            // Check ABI
            const abi = result.find((t) => t.symbol_code === "ABI");
            expect(abi).toBeDefined();
            expect(abi?.country_code).toBe("BELGIUM");
            expect(abi?.exchange_code).toBe(ExchangeAPI.BELGIUM.EURONEXT.getName());
            expect(abi?.primary_ticker_eodhd).toBe(`ABI.${ExchangeAPI.BELGIUM.EURONEXT.getExchangeCode()}`);
            expect(abi?.isin).toBe("BE0974293251");
            expect(abi?.type).toBe(TickerTypeDataBase.COMMON_STOCK);
        });

        it("should process Portuguese XLIS tickers correctly", () => {
            // Mock data for XLIS
            const mockXlisTickers = [
                {Code: "EDP", Name: "EDP - Energias de Portugal S.A.", Country: "Portugal", Exchange: "LS", Currency: "EUR", Type: "Common Stock", Isin: "PTEDP0AM0009"},
                {Code: "GALP", Name: "Galp Energia SGPS SA", Country: "Portugal", Exchange: "LS", Currency: "EUR", Type: "Common Stock", Isin: "PTGAL0AM0009"},
            ];

            // Process the tickers
            const result = processExchangeTickers(mockXlisTickers, ExchangeAPI.PORTUGAL.EURONEXT.getName(), ExchangeAPI.PORTUGAL.EURONEXT.getExchangeCode());

            // Verify results
            expect(result).toHaveLength(2);

            // Check EDP
            const edp = result.find((t) => t.symbol_code === "EDP");
            expect(edp).toBeDefined();
            expect(edp?.country_code).toBe("PORTUGAL");
            expect(edp?.exchange_code).toBe(ExchangeAPI.PORTUGAL.EURONEXT.getName());
            expect(edp?.primary_ticker_eodhd).toBe(`EDP.${ExchangeAPI.PORTUGAL.EURONEXT.getExchangeCode()}`);
            expect(edp?.isin).toBe("PTEDP0AM0009");
            expect(edp?.type).toBe(TickerTypeDataBase.COMMON_STOCK);
        });

        it("should process Irish XDUB tickers correctly", () => {
            // Mock data for XDUB
            const mockXdubTickers = [
                {Code: "RYA", Name: "Ryanair Holdings plc", Country: "Ireland", Exchange: "IR", Currency: "EUR", Type: "Common Stock", Isin: "IE00BYTBXV33"},
                {Code: "KRX", Name: "Kingspan Group PLC", Country: "Ireland", Exchange: "IR", Currency: "EUR", Type: "Common Stock", Isin: "IE0004927939"},
            ];

            // Process the tickers
            const result = processExchangeTickers(mockXdubTickers, ExchangeAPI.IRELAND.EURONEXT.getName(), ExchangeAPI.IRELAND.EURONEXT.getExchangeCode());

            // Verify results
            expect(result).toHaveLength(2);

            // Check RYA
            const rya = result.find((t) => t.symbol_code === "RYA");
            expect(rya).toBeDefined();
            expect(rya?.country_code).toBe("IRELAND");
            expect(rya?.exchange_code).toBe(ExchangeAPI.IRELAND.EURONEXT.getName());
            expect(rya?.primary_ticker_eodhd).toBe(`RYA.${ExchangeAPI.IRELAND.EURONEXT.getExchangeCode()}`);
            expect(rya?.isin).toBe("IE00BYTBXV33");
            expect(rya?.type).toBe(TickerTypeDataBase.COMMON_STOCK);
        });

        it("should process Norwegian XOSL tickers correctly", () => {
            // Mock data for XOSL
            const mockXoslTickers = [
                {Code: "EQNR", Name: "Equinor ASA", Country: "Norway", Exchange: "OL", Currency: "NOK", Type: "Common Stock", Isin: "NO0010096985"},
                {Code: "DNB", Name: "DNB Bank ASA", Country: "Norway", Exchange: "OL", Currency: "NOK", Type: "Common Stock", Isin: "NO0010031479"},
            ];

            // Process the tickers
            const result = processExchangeTickers(mockXoslTickers, ExchangeAPI.NORWAY.OSLO.getName(), ExchangeAPI.NORWAY.OSLO.getExchangeCode());

            // Verify results
            expect(result).toHaveLength(2);

            // Check EQNR
            const eqnr = result.find((t) => t.symbol_code === "EQNR");
            expect(eqnr).toBeDefined();
            expect(eqnr?.country_code).toBe("NORWAY");
            expect(eqnr?.exchange_code).toBe(ExchangeAPI.NORWAY.OSLO.getName());
            expect(eqnr?.primary_ticker_eodhd).toBe(`EQNR.${ExchangeAPI.NORWAY.OSLO.getExchangeCode()}`);
            expect(eqnr?.isin).toBe("NO0010096985");
            expect(eqnr?.type).toBe(TickerTypeDataBase.COMMON_STOCK);
        });

        it("should process UK LSE tickers correctly", () => {
            // Mock data for LSE
            const mockLseTickers = [{Code: "HSBA", Name: "HSBC Holdings PLC", Country: "UK", Exchange: "LSE", Currency: "GBX", Type: "Common Stock", Isin: "GB0005405286"}];

            // Process the tickers
            const result = processExchangeTickers(mockLseTickers, ExchangeAPI.UNITED_KINGDOM.LONDON.getName(), ExchangeAPI.UNITED_KINGDOM.LONDON.getExchangeCode());

            // Verify results
            expect(result).toHaveLength(1);

            // Check HSBA
            const hsba = result.find((t) => t.symbol_code === "HSBA");
            expect(hsba).toBeDefined();
            expect(hsba?.country_code).toBe("UK");
            expect(hsba?.exchange_code).toBe(ExchangeAPI.UNITED_KINGDOM.LONDON.getName());
            expect(hsba?.primary_ticker_eodhd).toBe(`HSBA.${ExchangeAPI.UNITED_KINGDOM.LONDON.getExchangeCode()}`);
            expect(hsba?.isin).toBe("GB0005405286");
            expect(hsba?.type).toBe(TickerTypeDataBase.COMMON_STOCK);
        });
    });
});

describe("populateListOfTickers - Unit Tests", () => {
    // Mock the repository and agenda functions
    const mockBulkCreateOrUpdate = jest.fn();
    const mockAddLogJobExecution = jest.fn();

    beforeEach(() => {
        jest.clearAllMocks();

        // Mock the ListOfTickersRepository
        const {ListOfTickersRepository} = require("../../src/repositories/implements/ListOfTickersRepository");
        ListOfTickersRepository.mockImplementation(() => ({
            bulkCreateOrUpdate: mockBulkCreateOrUpdate,
        }));

        // Mock the agenda function
        const {addLogJobExecution} = require("../../src/lib/agenda");
        addLogJobExecution.mockImplementation(mockAddLogJobExecution);
    });

    it("should populate tickers for all exchanges when no exchangeCodes parameter is provided", async () => {
        // Mock response data for all exchanges
        const mockNYSE = [{Code: "KO", Name: "The Coca-Cola Company", Country: "USA", Exchange: "NYSE", Currency: "USD", Type: "Common Stock", Isin: "US1912161007"}];
        const mockBVMF = [{Code: "PETR3", Name: "Petrobras", Country: "Brazil", Exchange: "SA", Currency: "BRL", Type: "Common Stock", Isin: "BRPETRACNOR9"}];

        // Setup mock implementations for all exchanges
        (apiEOD.populateTickers as jest.Mock).mockImplementation((exchange) => {
            switch (exchange) {
                case ExchangeAPI.USA.NYSE.getExchangeCode():
                    return Promise.resolve({data: mockNYSE});
                case ExchangeAPI.BRAZIL.BOVESPA.getExchangeCode():
                    return Promise.resolve({data: mockBVMF});
                default:
                    return Promise.resolve({data: []});
            }
        });

        // Mock the repository response
        const mockCreatedTickers = [
            {id: 1, symbol_code: "KO"},
            {id: 2, symbol_code: "PETR3"},
        ];
        mockBulkCreateOrUpdate.mockResolvedValue(mockCreatedTickers);

        // Call the function without parameters (should process all exchanges)
        const result = await populateListOfTickers();

        // Verify the function was called for all exchanges
        expect(apiEOD.populateTickers).toHaveBeenCalledWith(ExchangeAPI.USA.NYSE.getExchangeCode());
        expect(apiEOD.populateTickers).toHaveBeenCalledWith(ExchangeAPI.BRAZIL.BOVESPA.getExchangeCode());
        expect(apiEOD.populateTickers).toHaveBeenCalledWith(ExchangeAPI.GERMANY.XETRA.getExchangeCode());
        // ... and all other exchanges

        // Verify the repository was called
        expect(mockBulkCreateOrUpdate).toHaveBeenCalledTimes(1);
        expect(mockBulkCreateOrUpdate).toHaveBeenCalledWith(expect.any(Array));

        // Verify logging was called
        expect(mockAddLogJobExecution).toHaveBeenCalledWith(
            expect.any(String),
            "populateListOfTickers",
            expect.stringContaining("for all exchanges"),
            expect.objectContaining({exchangeCodes: undefined}),
        );

        // Verify the result
        expect(result).toEqual(mockCreatedTickers);
    });

    it("should populate tickers only for specified exchanges when exchangeCodes parameter is provided", async () => {
        // Mock response data for specific exchanges
        const mockBVMF = [{Code: "PETR3", Name: "Petrobras", Country: "Brazil", Exchange: "SA", Currency: "BRL", Type: "Common Stock", Isin: "BRPETRACNOR9"}];
        const mockXETR = [{Code: "SAP", Name: "SAP SE", Country: "Germany", Exchange: "XETRA", Currency: "EUR", Type: "Common Stock", Isin: "DE0007164600"}];

        // Setup mock implementations for specific exchanges only
        (apiEOD.populateTickers as jest.Mock).mockImplementation((exchange) => {
            switch (exchange) {
                case ExchangeAPI.BRAZIL.BOVESPA.getExchangeCode():
                    return Promise.resolve({data: mockBVMF});
                case ExchangeAPI.GERMANY.XETRA.getExchangeCode():
                    return Promise.resolve({data: mockXETR});
                default:
                    return Promise.resolve({data: []});
            }
        });

        // Mock the repository response
        const mockCreatedTickers = [
            {id: 1, symbol_code: "PETR3"},
            {id: 2, symbol_code: "SAP"},
        ];
        mockBulkCreateOrUpdate.mockResolvedValue(mockCreatedTickers);

        // Call the function with specific exchange codes
        const exchangeCodes = [ExchangeAPI.BRAZIL.BOVESPA.getExchangeCode(), ExchangeAPI.GERMANY.XETRA.getExchangeCode()];
        const result = await populateListOfTickers(exchangeCodes);

        // Verify the function was called only for specified exchanges
        expect(apiEOD.populateTickers).toHaveBeenCalledTimes(2);
        expect(apiEOD.populateTickers).toHaveBeenCalledWith(ExchangeAPI.BRAZIL.BOVESPA.getExchangeCode());
        expect(apiEOD.populateTickers).toHaveBeenCalledWith(ExchangeAPI.GERMANY.XETRA.getExchangeCode());

        // Verify the function was NOT called for other exchanges
        expect(apiEOD.populateTickers).not.toHaveBeenCalledWith(ExchangeAPI.USA.NYSE.getExchangeCode());
        expect(apiEOD.populateTickers).not.toHaveBeenCalledWith(ExchangeAPI.USA.NASDAQ.getExchangeCode());

        // Verify the repository was called
        expect(mockBulkCreateOrUpdate).toHaveBeenCalledTimes(1);
        expect(mockBulkCreateOrUpdate).toHaveBeenCalledWith(expect.any(Array));

        // Verify logging was called with correct parameters
        expect(mockAddLogJobExecution).toHaveBeenCalledWith(expect.any(String), "populateListOfTickers", expect.stringContaining("for exchanges: SA, XETRA"), expect.objectContaining({exchangeCodes}));

        // Verify the result
        expect(result).toEqual(mockCreatedTickers);
    });

    it("should handle empty exchange codes array", async () => {
        // Mock the repository response
        const mockCreatedTickers: any[] = [];
        mockBulkCreateOrUpdate.mockResolvedValue(mockCreatedTickers);

        // Call the function with empty array
        const result = await populateListOfTickers([]);

        // Verify no API calls were made
        expect(apiEOD.populateTickers).not.toHaveBeenCalled();

        // Verify the repository was still called (with empty array)
        expect(mockBulkCreateOrUpdate).toHaveBeenCalledTimes(1);
        expect(mockBulkCreateOrUpdate).toHaveBeenCalledWith([]);

        // Verify the result
        expect(result).toEqual(mockCreatedTickers);
    });

    it("should handle API errors gracefully and continue processing", async () => {
        // Setup mock to throw an error for one exchange but succeed for others
        (apiEOD.populateTickers as jest.Mock).mockImplementation((exchange) => {
            if (exchange === ExchangeAPI.BRAZIL.BOVESPA.getExchangeCode()) {
                return Promise.reject(new Error("API Error"));
            }
            return Promise.resolve({data: []});
        });

        // Mock the repository response
        const mockCreatedTickers: any[] = [];
        mockBulkCreateOrUpdate.mockResolvedValue(mockCreatedTickers);

        // Call the function - it should not throw but handle the error gracefully
        const result = await populateListOfTickers([ExchangeAPI.BRAZIL.BOVESPA.getExchangeCode()]);

        // Verify the function completed successfully despite the API error
        expect(result).toEqual(mockCreatedTickers);

        // Verify error logging was called for the initial log
        expect(mockAddLogJobExecution).toHaveBeenCalledWith(
            expect.any(String),
            "populateListOfTickers",
            expect.stringContaining("for exchanges: SA"),
            expect.objectContaining({exchangeCodes: [ExchangeAPI.BRAZIL.BOVESPA.getExchangeCode()]}),
        );

        // Verify the repository was still called (with empty array due to API error)
        expect(mockBulkCreateOrUpdate).toHaveBeenCalledTimes(1);
        expect(mockBulkCreateOrUpdate).toHaveBeenCalledWith([]);
    });
});
