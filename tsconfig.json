{"compilerOptions": {"target": "es2016", "module": "commonjs", "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "strict": true, "strictPropertyInitialization": false, "skipLibCheck": true, "outDir": "dist", "rootDir": ".", "jsx": "preserve", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "noEmit": false, "incremental": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "plugins": [{"name": "next"}]}, "ts-node": {"require": ["tsconfig-paths/register"]}, "include": ["src/**/*", "next-env.d.ts", ".next/types/**/*.ts"], "exclude": ["node_modules"]}