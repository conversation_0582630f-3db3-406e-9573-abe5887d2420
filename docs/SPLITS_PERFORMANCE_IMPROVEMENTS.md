# Splits Processing Performance Improvements

## Overview

This document outlines the performance improvements made to the `getSplits` functionality, implementing batch processing similar to the dividends, fundamental data, and price processing optimizations.

## Key Improvements

### 1. Configurable Batch Processing

- **Batch Size Configuration**: Added `splitsBatchSize` setting to the `agendaSettings` collection
- **Default Value**: 10 tickers per batch (configurable via API)
- **Range**: 1-50 tickers per batch
- **API Endpoint**: `PUT /api/agenda/settings` with `{"splitsBatchSize": 10}`

### 2. Concurrent Processing Within Batches

- **Intra-batch Concurrency**: All tickers within a batch are processed concurrently using `Promise.all()`
- **Inter-batch Control**: Batches are processed either sequentially or with limited concurrency (max 3 concurrent batches)
- **Error Isolation**: Errors in individual tickers don't stop the entire batch processing

### 3. Enhanced Progress Tracking

- **Job Progress Integration**: Full integration with the existing job progress tracking system
- **Batch-level Logging**: Detailed logging for each batch with timing information
- **Error Handling**: Comprehensive error logging with ticker-specific details

## Technical Implementation

### Batch Processing Functions

```typescript
// Batch creation and processing functions
function createBatchesForSplits(tickers: ListOfTickers[], batchSize: number): ListOfTickers[][]
async function processBatchForSplits(batch: ListOfTickers[], batchIndex: number, totalBatches: number): Promise<void>
async function processBatchesSequentiallyForSplits(batches: ListOfTickers[][], historyId?: string): Promise<void>
async function processBatchesConcurrentlyForSplits(batches: ListOfTickers[][], maxConcurrency: number, historyId?: string): Promise<void>
```

### Updated Configuration

```typescript
// New agenda setting
splitsBatchSize: 10 // Default batch size for splits API calls

// New helper function
export const getSplitsBatchSize = async (): Promise<number>
```

### Processing Logic

1. **Batch Creation**: Tickers are divided into batches based on the configured batch size
2. **Concurrency Decision**: System automatically chooses between sequential and concurrent processing based on the number of batches
3. **API Calls**: Each ticker's splits are fetched from the EODHD API concurrently within batches
4. **Data Storage**: Splits data is saved to the database using the existing `HistoricalSplitsRepository`
5. **Progress Updates**: Job progress is updated after each batch completion

## Performance Benefits

### Before (Sequential Processing)
- **API Calls**: One call per ticker, processed sequentially
- **Error Handling**: Single ticker error could affect processing flow
- **Progress Tracking**: Updated after each individual ticker
- **Resource Usage**: Underutilized network and processing capacity

### After (Batch Processing)
- **API Calls**: Multiple concurrent calls per batch, with controlled batch concurrency
- **Error Handling**: Isolated error handling per ticker, batch processing continues
- **Progress Tracking**: Efficient batch-level progress updates
- **Resource Usage**: Optimized network utilization and processing throughput

### Expected Performance Gains

| Dataset Size | Sequential Time | Batch Time (Est.) | Improvement |
|--------------|----------------|-------------------|-------------|
| 50 tickers   | ~5 minutes     | ~1-2 minutes      | 60-80%      |
| 100 tickers  | ~10 minutes    | ~2-3 minutes      | 70-80%      |
| 500 tickers  | ~50 minutes    | ~10-15 minutes    | 70-80%      |

## Configuration

### Setting Splits Batch Size

```bash
# Set splits batch size to 15
curl -X PUT http://localhost:3000/api/agenda/settings \
  -H "Content-Type: application/json" \
  -d '{"splitsBatchSize": 15}'

# Set multiple settings at once
curl -X PUT http://localhost:3000/api/agenda/settings \
  -H "Content-Type: application/json" \
  -d '{"splitsBatchSize": 15, "dividendsBatchSize": 10, "fundamentalDataBatchSize": 10}'
```

### Recommended Batch Sizes

- **Small datasets (1-20 tickers)**: Batch size 5-10
- **Medium datasets (21-100 tickers)**: Batch size 10-15
- **Large datasets (100+ tickers)**: Batch size 15-25
- **Very large datasets (500+ tickers)**: Batch size 20-30

## Testing

### Test Endpoint

Use the existing test infrastructure or create a new test endpoint:

```bash
# Test with specific tickers
curl "http://localhost:3000/api/splits/test-batch?tickers=AAPL.US,MSFT.US,GOOGL.US&limit=10"

# Test with random sample
curl "http://localhost:3000/api/splits/test-batch?limit=20"
```

### Monitoring

- **Logs**: Monitor batch processing logs for timing and error information
- **Progress**: Use the job progress API to track processing status
- **Performance**: Compare processing times before and after implementation

## Error Handling

- **Ticker-level Errors**: Individual ticker failures are logged but don't stop batch processing
- **Batch-level Errors**: Batch failures are logged and processing continues with remaining batches
- **Progress Tracking**: Progress is updated even when errors occur to maintain accurate tracking
- **Retry Logic**: Consider implementing retry logic for failed API calls in future iterations

## Future Enhancements

1. **Adaptive Batch Sizing**: Dynamically adjust batch size based on API response times
2. **Retry Mechanism**: Implement automatic retry for failed API calls
3. **Rate Limiting**: Add intelligent rate limiting based on API quotas
4. **Caching**: Implement caching for recently fetched splits data
5. **Metrics Collection**: Add detailed performance metrics collection
