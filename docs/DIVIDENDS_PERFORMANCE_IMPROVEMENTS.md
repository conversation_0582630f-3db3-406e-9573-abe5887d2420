# Dividends Processing Performance Improvements

## Overview

This document outlines the performance improvements made to the `getDividendsController` functionality, implementing batch processing similar to the price processing optimizations.

## Key Improvements

### 1. Configurable Batch Processing

- **Batch Size Configuration**: Added `dividendsBatchSize` setting to the `agendaSettings` collection
- **Default Value**: 10 tickers per batch (configurable via API)
- **Range**: 1-50 tickers per batch
- **API Endpoint**: `PUT /api/agenda/settings` with `{"dividendsBatchSize": 10}`

### 2. Concurrent Processing Within Batches

- **Concurrent API Calls**: Process multiple dividend requests simultaneously within each batch
- **Limited Concurrency**: Maximum 3 concurrent batches to avoid overwhelming the API
- **Fallback Handling**: Individual ticker processing continues even if some fail

### 3. Intelligent Batch Strategy

- **Sequential vs Concurrent**: Automatically chooses processing strategy based on batch count
- **Progress Tracking**: Real-time progress updates for job monitoring
- **Error Isolation**: Batch failures don't stop overall processing

### 4. Enhanced Logging and Monitoring

- **Batch-level Logging**: Detailed logs for each batch with timing information
- **Error Tracking**: Individual ticker error logging with context
- **Performance Metrics**: Duration tracking per batch and overall processing

## Technical Implementation

### Batch Processing Flow

1. **Batch Creation**: Divide tickers into configurable batch sizes
2. **Strategy Selection**: Choose sequential or concurrent processing
3. **Concurrent Execution**: Process tickers within each batch simultaneously
4. **Progress Updates**: Update job progress after each batch completion
5. **Error Handling**: Continue processing despite individual failures

### Key Functions

- `createBatches()`: Divides tickers into manageable batches
- `processBatch()`: Handles concurrent processing within a batch
- `processBatchesSequentially()`: Sequential batch processing with progress tracking
- `processBatchesConcurrently()`: Concurrent batch processing with limited concurrency

## Performance Benefits

### Expected Improvements

- **Processing Time**: 40-60% faster overall processing for large ticker sets
- **Resource Efficiency**: Better CPU and memory utilization through controlled concurrency
- **Error Resilience**: Improved fault tolerance with isolated error handling
- **Monitoring**: Enhanced visibility into processing progress and performance

### Comparison with Sequential Processing

| Metric | Sequential (Old) | Batch Processing (New) |
|--------|------------------|------------------------|
| Concurrency | None | Up to 10 per batch |
| Error Handling | Stop on error | Continue processing |
| Progress Tracking | Per ticker | Per batch |
| Resource Usage | Linear | Optimized |

## Configuration

### Setting Dividends Batch Size

```bash
# Set dividends batch size to 15
curl -X PUT http://localhost:3000/api/agenda/settings \
  -H "Content-Type: application/json" \
  -d '{"dividendsBatchSize": 15}'

# Set multiple settings at once
curl -X PUT http://localhost:3000/api/agenda/settings \
  -H "Content-Type: application/json" \
  -d '{"dividendsBatchSize": 15, "priceBatchSize": 25}'
```

### Getting Current Settings

```bash
# Get current settings
curl http://localhost:3000/api/agenda/settings
```

## Testing

### Test Endpoint

A test endpoint is available at `/api/dividends/test-batch` to verify the improvements:

```bash
# Test with 10 random tickers
curl "http://localhost:3000/api/dividends/test-batch?limit=10"

# Test with specific tickers
curl "http://localhost:3000/api/dividends/test-batch?tickers=AAPL.US,MSFT.US,GOOGL.US"
```

### Response Example

```json
{
  "success": true,
  "testResults": {
    "tickersProcessed": 10,
    "durationMs": 2500,
    "averageTimePerTicker": 250,
    "tickerCodes": ["AAPL.US", "MSFT.US", "GOOGL.US", ...]
  },
  "message": "Dividends batch processing test completed successfully"
}
```

## Migration Notes

### Backward Compatibility

- The `getDividendsController` function signature remains unchanged
- Existing job calls will automatically use the new batch processing
- Default batch size ensures conservative resource usage

### Monitoring

- Enhanced logging provides visibility into batch processing performance
- Job progress tracking works seamlessly with existing job monitoring systems
- Error logs include batch context for better debugging

## Best Practices

### Batch Size Tuning

- **Small datasets (< 50 tickers)**: Use default batch size (10)
- **Medium datasets (50-200 tickers)**: Consider batch size 15-20
- **Large datasets (> 200 tickers)**: Use batch size 20-30
- **API rate limits**: Adjust based on EODHD API limitations

### Error Handling

- Monitor logs for recurring ticker-specific errors
- Batch failures are logged but don't stop overall processing
- Individual ticker errors are isolated and logged with context

### Performance Monitoring

- Track average processing time per ticker
- Monitor batch completion times
- Watch for API rate limit warnings in logs
